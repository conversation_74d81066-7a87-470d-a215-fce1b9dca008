import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Tabs,
  Tab,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  CardActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Category as CategoryIcon,
  Business as BusinessIcon,
  Assignment as AssignmentIcon,
  Cable as CableIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import tipologieCaviService from '../../services/tipologieCaviService';

const TipologieCaviManager = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Stati per categorie
  const [categorie, setCategorie] = useState([]);
  const [categoriaDialog, setCategoriaDialog] = useState(false);
  const [categoriaForm, setCategoriaForm] = useState({
    nome_categoria: '',
    descrizione: '',
    id_categoria_padre: null,
    livello: 1,
    ordine_visualizzazione: 0,
    attiva: true
  });
  const [editingCategoria, setEditingCategoria] = useState(null);

  // Stati per produttori
  const [produttori, setProduttori] = useState([]);
  const [produttoreDialog, setProduttoreDialog] = useState(false);
  const [produttoreForm, setProduttoreForm] = useState({
    nome_produttore: '',
    paese: '',
    sito_web: '',
    email_contatto: '',
    telefono: '',
    note: '',
    attivo: true
  });
  const [editingProduttore, setEditingProduttore] = useState(null);

  // Stati per standard
  const [standard, setStandard] = useState([]);
  const [standardDialog, setStandardDialog] = useState(false);
  const [standardForm, setStandardForm] = useState({
    nome_standard: '',
    ente_normativo: '',
    descrizione: '',
    anno_pubblicazione: null,
    versione: '',
    url_documento: '',
    attivo: true
  });
  const [editingStandard, setEditingStandard] = useState(null);

  // Stati per tipologie
  const [tipologie, setTipologie] = useState([]);
  const [tipologieTotal, setTipologieTotal] = useState(0);
  const [tipologiePage, setTipologiePage] = useState(1);
  const [tipologiePageSize] = useState(20);
  const [tipologiaDialog, setTipologiaDialog] = useState(false);
  const [tipologiaForm, setTipologiaForm] = useState({
    codice_prodotto: '',
    nome_commerciale: '',
    id_produttore: null,
    id_categoria: null,
    id_standard_principale: null,
    descrizione_breve: '',
    descrizione_completa: '',
    materiale_guaina_esterna: '',
    diametro_esterno_mm: null,
    peso_kg_per_km: null,
    temperatura_min_celsius: null,
    temperatura_max_celsius: null,
    raggio_curvatura_min_mm: null,
    resistente_uv: false,
    resistente_olio: false,
    resistente_fiamma: false,
    per_esterno: false,
    per_interrato: false,
    scheda_tecnica_url: '',
    immagine_url: '',
    prezzo_indicativo_euro_per_metro: null,
    disponibile: true,
    note: ''
  });
  const [editingTipologia, setEditingTipologia] = useState(null);

  // Filtri per tipologie
  const [filtriTipologie, setFiltriTipologie] = useState({
    categoria_id: null,
    produttore_id: null,
    disponibile: null,
    search_text: ''
  });

  useEffect(() => {
    loadData();
  }, [tabValue]);

  const loadData = async () => {
    setLoading(true);
    try {
      switch (tabValue) {
        case 0:
          await loadCategorie();
          break;
        case 1:
          await loadProduttori();
          break;
        case 2:
          await loadStandard();
          break;
        case 3:
          // Per le tipologie, carica anche categorie, produttori e standard
          await Promise.all([
            loadTipologie(),
            loadCategorie(),
            loadProduttori(),
            loadStandard()
          ]);
          break;
      }
    } catch (error) {
      setError('Errore nel caricamento dei dati: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadCategorie = async () => {
    const data = await tipologieCaviService.getCategorie();
    setCategorie(data);
  };

  const loadProduttori = async () => {
    const data = await tipologieCaviService.getProduttori();
    setProduttori(data);
  };

  const loadStandard = async () => {
    const data = await tipologieCaviService.getStandard();
    setStandard(data);
  };

  const loadTipologie = async () => {
    const params = {
      page: tipologiePage,
      page_size: tipologiePageSize,
      ...filtriTipologie
    };
    const data = await tipologieCaviService.getTipologie(params);
    setTipologie(data.tipologie);
    setTipologieTotal(data.total_count);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setError('');
    setSuccess('');
  };

  // Funzioni per categorie
  const handleCreateCategoria = () => {
    setCategoriaForm({
      nome_categoria: '',
      descrizione: '',
      id_categoria_padre: null,
      livello: 1,
      ordine_visualizzazione: 0,
      attiva: true
    });
    setEditingCategoria(null);
    setCategoriaDialog(true);
  };

  const handleEditCategoria = (categoria) => {
    setCategoriaForm(categoria);
    setEditingCategoria(categoria.id_categoria);
    setCategoriaDialog(true);
  };

  const handleSaveCategoria = async () => {
    try {
      if (editingCategoria) {
        await tipologieCaviService.updateCategoria(editingCategoria, categoriaForm);
        setSuccess('Categoria aggiornata con successo');
      } else {
        await tipologieCaviService.createCategoria(categoriaForm);
        setSuccess('Categoria creata con successo');
      }
      setCategoriaDialog(false);
      await loadCategorie();
    } catch (error) {
      setError('Errore nel salvataggio: ' + error.message);
    }
  };

  const handleDeleteCategoria = async (id) => {
    if (window.confirm('Sei sicuro di voler eliminare questa categoria?')) {
      try {
        await tipologieCaviService.deleteCategoria(id);
        setSuccess('Categoria eliminata con successo');
        await loadCategorie();
      } catch (error) {
        setError('Errore nell\'eliminazione: ' + error.message);
      }
    }
  };

  // Funzioni per produttori
  const handleCreateProduttore = () => {
    setProduttoreForm({
      nome_produttore: '',
      paese: '',
      sito_web: '',
      email_contatto: '',
      telefono: '',
      note: '',
      attivo: true
    });
    setEditingProduttore(null);
    setProduttoreDialog(true);
  };

  const handleEditProduttore = (produttore) => {
    setProduttoreForm(produttore);
    setEditingProduttore(produttore.id_produttore);
    setProduttoreDialog(true);
  };

  const handleSaveProduttore = async () => {
    try {
      if (editingProduttore) {
        await tipologieCaviService.updateProduttore(editingProduttore, produttoreForm);
        setSuccess('Produttore aggiornato con successo');
      } else {
        await tipologieCaviService.createProduttore(produttoreForm);
        setSuccess('Produttore creato con successo');
      }
      setProduttoreDialog(false);
      await loadProduttori();
    } catch (error) {
      setError('Errore nel salvataggio: ' + error.message);
    }
  };

  const handleDeleteProduttore = async (id) => {
    if (window.confirm('Sei sicuro di voler eliminare questo produttore?')) {
      try {
        await tipologieCaviService.deleteProduttore(id);
        setSuccess('Produttore eliminato con successo');
        await loadProduttori();
      } catch (error) {
        setError('Errore nell\'eliminazione: ' + error.message);
      }
    }
  };

  // Funzioni per tipologie
  const handleCreateTipologia = () => {
    setTipologiaForm({
      codice_prodotto: '',
      nome_commerciale: '',
      id_produttore: null,
      id_categoria: null,
      id_standard_principale: null,
      descrizione_breve: '',
      descrizione_completa: '',
      materiale_guaina_esterna: '',
      diametro_esterno_mm: null,
      peso_kg_per_km: null,
      temperatura_min_celsius: null,
      temperatura_max_celsius: null,
      raggio_curvatura_min_mm: null,
      resistente_uv: false,
      resistente_olio: false,
      resistente_fiamma: false,
      per_esterno: false,
      per_interrato: false,
      scheda_tecnica_url: '',
      immagine_url: '',
      prezzo_indicativo_euro_per_metro: null,
      disponibile: true,
      note: ''
    });
    setEditingTipologia(null);
    setTipologiaDialog(true);
  };

  const handleEditTipologia = (tipologia) => {
    setTipologiaForm(tipologia);
    setEditingTipologia(tipologia.id_tipologia);
    setTipologiaDialog(true);
  };

  const handleSaveTipologia = async () => {
    try {
      if (editingTipologia) {
        await tipologieCaviService.updateTipologia(editingTipologia, tipologiaForm);
        setSuccess('Tipologia aggiornata con successo');
      } else {
        await tipologieCaviService.createTipologia(tipologiaForm);
        setSuccess('Tipologia creata con successo');
      }
      setTipologiaDialog(false);
      await loadTipologie();
    } catch (error) {
      setError('Errore nel salvataggio: ' + error.message);
    }
  };

  const handleDeleteTipologia = async (id) => {
    if (window.confirm('Sei sicuro di voler eliminare questa tipologia?')) {
      try {
        await tipologieCaviService.deleteTipologia(id);
        setSuccess('Tipologia eliminata con successo');
        await loadTipologie();
      } catch (error) {
        setError('Errore nell\'eliminazione: ' + error.message);
      }
    }
  };

  // Funzioni per standard
  const handleCreateStandard = () => {
    setStandardForm({
      nome_standard: '',
      ente_normativo: '',
      descrizione: '',
      anno_pubblicazione: null,
      versione: '',
      url_documento: '',
      attivo: true
    });
    setEditingStandard(null);
    setStandardDialog(true);
  };

  const handleEditStandard = (std) => {
    setStandardForm(std);
    setEditingStandard(std.id_standard);
    setStandardDialog(true);
  };

  const handleSaveStandard = async () => {
    try {
      if (editingStandard) {
        await tipologieCaviService.updateStandard(editingStandard, standardForm);
        setSuccess('Standard aggiornato con successo');
      } else {
        await tipologieCaviService.createStandard(standardForm);
        setSuccess('Standard creato con successo');
      }
      setStandardDialog(false);
      await loadStandard();
    } catch (error) {
      setError('Errore nel salvataggio: ' + error.message);
    }
  };

  const handleDeleteStandard = async (id) => {
    if (window.confirm('Sei sicuro di voler eliminare questo standard?')) {
      try {
        await tipologieCaviService.deleteStandard(id);
        setSuccess('Standard eliminato con successo');
        await loadStandard();
      } catch (error) {
        setError('Errore nell\'eliminazione: ' + error.message);
      }
    }
  };

  const renderCategorieTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Gestione Categorie Cavi</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateCategoria}
        >
          Nuova Categoria
        </Button>
      </Box>

      {loading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Descrizione</TableCell>
                <TableCell>Livello</TableCell>
                <TableCell>Categoria Padre</TableCell>
                <TableCell>Attiva</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {categorie.map((categoria) => (
                <TableRow key={categoria.id_categoria}>
                  <TableCell>{categoria.nome_categoria}</TableCell>
                  <TableCell>{categoria.descrizione}</TableCell>
                  <TableCell>{categoria.livello}</TableCell>
                  <TableCell>
                    {categoria.categoria_padre?.nome_categoria || '-'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={categoria.attiva ? 'Sì' : 'No'}
                      color={categoria.attiva ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditCategoria(categoria)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteCategoria(categoria.id_categoria)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderProduttoriTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Gestione Produttori Cavi</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateProduttore}
        >
          Nuovo Produttore
        </Button>
      </Box>

      {loading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Paese</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Telefono</TableCell>
                <TableCell>Attivo</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {produttori.map((produttore) => (
                <TableRow key={produttore.id_produttore}>
                  <TableCell>{produttore.nome_produttore}</TableCell>
                  <TableCell>{produttore.paese}</TableCell>
                  <TableCell>{produttore.email_contatto}</TableCell>
                  <TableCell>{produttore.telefono}</TableCell>
                  <TableCell>
                    <Chip
                      label={produttore.attivo ? 'Sì' : 'No'}
                      color={produttore.attivo ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditProduttore(produttore)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteProduttore(produttore.id_produttore)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderStandardTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Gestione Standard Cavi</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateStandard}
        >
          Nuovo Standard
        </Button>
      </Box>

      {loading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Ente Normativo</TableCell>
                <TableCell>Anno</TableCell>
                <TableCell>Versione</TableCell>
                <TableCell>Attivo</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {standard.map((std) => (
                <TableRow key={std.id_standard}>
                  <TableCell>{std.nome_standard}</TableCell>
                  <TableCell>{std.ente_normativo}</TableCell>
                  <TableCell>{std.anno_pubblicazione}</TableCell>
                  <TableCell>{std.versione}</TableCell>
                  <TableCell>
                    <Chip
                      label={std.attivo ? 'Sì' : 'No'}
                      color={std.attivo ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditStandard(std)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteStandard(std.id_standard)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderTipologieTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Gestione Tipologie Cavi</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateTipologia}
        >
          Nuova Tipologia
        </Button>
      </Box>

      {/* Filtri */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Ricerca"
              value={filtriTipologie.search_text}
              onChange={(e) => setFiltriTipologie({...filtriTipologie, search_text: e.target.value})}
              InputProps={{
                startAdornment: <SearchIcon />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Categoria</InputLabel>
              <Select
                value={filtriTipologie.categoria_id || ''}
                onChange={(e) => setFiltriTipologie({...filtriTipologie, categoria_id: e.target.value || null})}
              >
                <MenuItem value="">Tutte</MenuItem>
                {categorie.map((cat) => (
                  <MenuItem key={cat.id_categoria} value={cat.id_categoria}>
                    {cat.nome_categoria}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Produttore</InputLabel>
              <Select
                value={filtriTipologie.produttore_id || ''}
                onChange={(e) => setFiltriTipologie({...filtriTipologie, produttore_id: e.target.value || null})}
              >
                <MenuItem value="">Tutti</MenuItem>
                {produttori.map((prod) => (
                  <MenuItem key={prod.id_produttore} value={prod.id_produttore}>
                    {prod.nome_produttore}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              variant="outlined"
              fullWidth
              onClick={loadTipologie}
              sx={{ height: '56px' }}
            >
              Applica Filtri
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Codice Prodotto</TableCell>
                <TableCell>Nome Commerciale</TableCell>
                <TableCell>Produttore</TableCell>
                <TableCell>Categoria</TableCell>
                <TableCell>Disponibile</TableCell>
                <TableCell>Azioni</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tipologie.map((tipologia) => (
                <TableRow key={tipologia.id_tipologia}>
                  <TableCell>{tipologia.codice_prodotto}</TableCell>
                  <TableCell>{tipologia.nome_commerciale}</TableCell>
                  <TableCell>{tipologia.produttore?.nome_produttore || '-'}</TableCell>
                  <TableCell>{tipologia.categoria?.nome_categoria || '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={tipologia.disponibile ? 'Sì' : 'No'}
                      color={tipologia.disponibile ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditTipologia(tipologia)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteTipologia(tipologia.id_tipologia)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  return (
    <Box sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<CategoryIcon />} label="Categorie" />
          <Tab icon={<BusinessIcon />} label="Produttori" />
          <Tab icon={<AssignmentIcon />} label="Standard" />
          <Tab icon={<CableIcon />} label="Tipologie" />
        </Tabs>
      </Paper>

      <Box sx={{ mt: 2 }}>
        {tabValue === 0 && renderCategorieTab()}
        {tabValue === 1 && renderProduttoriTab()}
        {tabValue === 2 && renderStandardTab()}
        {tabValue === 3 && renderTipologieTab()}
      </Box>

      {/* Dialog per Categoria */}
      <Dialog open={categoriaDialog} onClose={() => setCategoriaDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCategoria ? 'Modifica Categoria' : 'Nuova Categoria'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nome Categoria"
                value={categoriaForm.nome_categoria}
                onChange={(e) => setCategoriaForm({...categoriaForm, nome_categoria: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Categoria Padre</InputLabel>
                <Select
                  value={categoriaForm.id_categoria_padre || ''}
                  onChange={(e) => setCategoriaForm({...categoriaForm, id_categoria_padre: e.target.value || null})}
                >
                  <MenuItem value="">Nessuna (Categoria principale)</MenuItem>
                  {categorie.filter(c => c.livello < 3).map((cat) => (
                    <MenuItem key={cat.id_categoria} value={cat.id_categoria}>
                      {cat.nome_categoria}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrizione"
                value={categoriaForm.descrizione}
                onChange={(e) => setCategoriaForm({...categoriaForm, descrizione: e.target.value})}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Ordine Visualizzazione"
                type="number"
                value={categoriaForm.ordine_visualizzazione}
                onChange={(e) => setCategoriaForm({...categoriaForm, ordine_visualizzazione: parseInt(e.target.value) || 0})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={categoriaForm.attiva}
                    onChange={(e) => setCategoriaForm({...categoriaForm, attiva: e.target.checked})}
                  />
                }
                label="Categoria Attiva"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCategoriaDialog(false)}>Annulla</Button>
          <Button onClick={handleSaveCategoria} variant="contained">
            {editingCategoria ? 'Aggiorna' : 'Crea'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per Produttore */}
      <Dialog open={produttoreDialog} onClose={() => setProduttoreDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingProduttore ? 'Modifica Produttore' : 'Nuovo Produttore'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nome Produttore"
                value={produttoreForm.nome_produttore}
                onChange={(e) => setProduttoreForm({...produttoreForm, nome_produttore: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Paese"
                value={produttoreForm.paese}
                onChange={(e) => setProduttoreForm({...produttoreForm, paese: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email Contatto"
                type="email"
                value={produttoreForm.email_contatto}
                onChange={(e) => setProduttoreForm({...produttoreForm, email_contatto: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Telefono"
                value={produttoreForm.telefono}
                onChange={(e) => setProduttoreForm({...produttoreForm, telefono: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Sito Web"
                value={produttoreForm.sito_web}
                onChange={(e) => setProduttoreForm({...produttoreForm, sito_web: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Note"
                value={produttoreForm.note}
                onChange={(e) => setProduttoreForm({...produttoreForm, note: e.target.value})}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={produttoreForm.attivo}
                    onChange={(e) => setProduttoreForm({...produttoreForm, attivo: e.target.checked})}
                  />
                }
                label="Produttore Attivo"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setProduttoreDialog(false)}>Annulla</Button>
          <Button onClick={handleSaveProduttore} variant="contained">
            {editingProduttore ? 'Aggiorna' : 'Crea'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per Standard */}
      <Dialog open={standardDialog} onClose={() => setStandardDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingStandard ? 'Modifica Standard' : 'Nuovo Standard'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nome Standard"
                value={standardForm.nome_standard}
                onChange={(e) => setStandardForm({...standardForm, nome_standard: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Ente Normativo"
                value={standardForm.ente_normativo}
                onChange={(e) => setStandardForm({...standardForm, ente_normativo: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrizione"
                value={standardForm.descrizione}
                onChange={(e) => setStandardForm({...standardForm, descrizione: e.target.value})}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Anno Pubblicazione"
                type="number"
                value={standardForm.anno_pubblicazione || ''}
                onChange={(e) => setStandardForm({...standardForm, anno_pubblicazione: parseInt(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Versione"
                value={standardForm.versione}
                onChange={(e) => setStandardForm({...standardForm, versione: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={standardForm.attivo}
                    onChange={(e) => setStandardForm({...standardForm, attivo: e.target.checked})}
                  />
                }
                label="Standard Attivo"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="URL Documento"
                value={standardForm.url_documento}
                onChange={(e) => setStandardForm({...standardForm, url_documento: e.target.value})}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStandardDialog(false)}>Annulla</Button>
          <Button onClick={handleSaveStandard} variant="contained">
            {editingStandard ? 'Aggiorna' : 'Crea'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per Tipologia */}
      <Dialog open={tipologiaDialog} onClose={() => setTipologiaDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          {editingTipologia ? 'Modifica Tipologia' : 'Nuova Tipologia'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Codice Prodotto"
                value={tipologiaForm.codice_prodotto}
                onChange={(e) => setTipologiaForm({...tipologiaForm, codice_prodotto: e.target.value})}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nome Commerciale"
                value={tipologiaForm.nome_commerciale}
                onChange={(e) => setTipologiaForm({...tipologiaForm, nome_commerciale: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Categoria</InputLabel>
                <Select
                  value={tipologiaForm.id_categoria || ''}
                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_categoria: e.target.value || null})}
                >
                  {categorie.map((cat) => (
                    <MenuItem key={cat.id_categoria} value={cat.id_categoria}>
                      {cat.nome_categoria}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Produttore</InputLabel>
                <Select
                  value={tipologiaForm.id_produttore || ''}
                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_produttore: e.target.value || null})}
                >
                  <MenuItem value="">Nessuno</MenuItem>
                  {produttori.map((prod) => (
                    <MenuItem key={prod.id_produttore} value={prod.id_produttore}>
                      {prod.nome_produttore}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Standard Principale</InputLabel>
                <Select
                  value={tipologiaForm.id_standard_principale || ''}
                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_standard_principale: e.target.value || null})}
                >
                  <MenuItem value="">Nessuno</MenuItem>
                  {standard.map((std) => (
                    <MenuItem key={std.id_standard} value={std.id_standard}>
                      {std.nome_standard}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Descrizione Breve"
                value={tipologiaForm.descrizione_breve}
                onChange={(e) => setTipologiaForm({...tipologiaForm, descrizione_breve: e.target.value})}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Materiale Guaina Esterna"
                value={tipologiaForm.materiale_guaina_esterna}
                onChange={(e) => setTipologiaForm({...tipologiaForm, materiale_guaina_esterna: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Diametro Esterno (mm)"
                type="number"
                value={tipologiaForm.diametro_esterno_mm || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, diametro_esterno_mm: parseFloat(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Peso (kg/km)"
                type="number"
                value={tipologiaForm.peso_kg_per_km || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, peso_kg_per_km: parseFloat(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Temp. Min (°C)"
                type="number"
                value={tipologiaForm.temperatura_min_celsius || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, temperatura_min_celsius: parseInt(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Temp. Max (°C)"
                type="number"
                value={tipologiaForm.temperatura_max_celsius || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, temperatura_max_celsius: parseInt(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Prezzo Indicativo (€/m)"
                type="number"
                step="0.0001"
                value={tipologiaForm.prezzo_indicativo_euro_per_metro || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, prezzo_indicativo_euro_per_metro: parseFloat(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Raggio Curvatura Min (mm)"
                type="number"
                value={tipologiaForm.raggio_curvatura_min_mm || ''}
                onChange={(e) => setTipologiaForm({...tipologiaForm, raggio_curvatura_min_mm: parseFloat(e.target.value) || null})}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.resistente_uv}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_uv: e.target.checked})}
                  />
                }
                label="Resistente UV"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.resistente_olio}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_olio: e.target.checked})}
                  />
                }
                label="Resistente Olio"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.resistente_fiamma}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_fiamma: e.target.checked})}
                  />
                }
                label="Resistente Fiamma"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.disponibile}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, disponibile: e.target.checked})}
                  />
                }
                label="Disponibile"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.per_esterno}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, per_esterno: e.target.checked})}
                  />
                }
                label="Per Esterno"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tipologiaForm.per_interrato}
                    onChange={(e) => setTipologiaForm({...tipologiaForm, per_interrato: e.target.checked})}
                  />
                }
                label="Per Interrato"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Note"
                value={tipologiaForm.note}
                onChange={(e) => setTipologiaForm({...tipologiaForm, note: e.target.value})}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTipologiaDialog(false)}>Annulla</Button>
          <Button onClick={handleSaveTipologia} variant="contained">
            {editingTipologia ? 'Aggiorna' : 'Crea'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TipologieCaviManager;
