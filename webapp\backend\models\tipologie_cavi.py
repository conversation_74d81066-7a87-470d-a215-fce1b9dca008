#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modelli SQLAlchemy per il database delle tipologie di cavi.
Gestisce categorie, produttori, standard e tipologie di cavi.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DECIMAL, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from backend.database import Base


class CategoriaCavo(Base):
    """Modello per le categorie di cavi (struttura gerarchica)."""
    __tablename__ = "categorie_cavi"

    id_categoria = Column(Integer, primary_key=True, autoincrement=True)
    nome_categoria = Column(String(100), nullable=False, unique=True)
    descrizione = Column(Text)
    id_categoria_padre = Column(Integer, ForeignKey("categorie_cavi.id_categoria"), nullable=True)
    livello = Column(Integer, nullable=False, default=1)
    ordine_visualizzazione = Column(Integer, default=0)
    attiva = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relazioni
    categoria_padre = relationship("CategoriaCavo", remote_side=[id_categoria], backref="sottocategorie")
    tipologie = relationship("TipologiaCavo", back_populates="categoria")

    def __repr__(self):
        return f"<CategoriaCavo(id={self.id_categoria}, nome='{self.nome_categoria}', livello={self.livello})>"


class ProduttoreCavo(Base):
    """Modello per i produttori di cavi."""
    __tablename__ = "produttori_cavi"

    id_produttore = Column(Integer, primary_key=True, autoincrement=True)
    nome_produttore = Column(String(100), nullable=False, unique=True)
    paese = Column(String(50))
    sito_web = Column(String(200))
    email_contatto = Column(String(100))
    telefono = Column(String(50))
    note = Column(Text)
    attivo = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relazioni
    tipologie = relationship("TipologiaCavo", back_populates="produttore")

    def __repr__(self):
        return f"<ProduttoreCavo(id={self.id_produttore}, nome='{self.nome_produttore}', paese='{self.paese}')>"


class StandardCavo(Base):
    """Modello per gli standard e normative dei cavi."""
    __tablename__ = "standard_cavi"

    id_standard = Column(Integer, primary_key=True, autoincrement=True)
    nome_standard = Column(String(50), nullable=False, unique=True)
    ente_normativo = Column(String(50))
    descrizione = Column(Text)
    anno_pubblicazione = Column(Integer)
    versione = Column(String(20))
    url_documento = Column(String(300))
    attivo = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relazioni
    tipologie_standard = relationship("TipologiaStandard", back_populates="standard")

    def __repr__(self):
        return f"<StandardCavo(id={self.id_standard}, nome='{self.nome_standard}', ente='{self.ente_normativo}')>"


class TipologiaCavo(Base):
    """Modello principale per le tipologie di cavi."""
    __tablename__ = "tipologie_cavi"

    id_tipologia = Column(Integer, primary_key=True, autoincrement=True)
    codice_prodotto = Column(String(100), nullable=False)
    nome_commerciale = Column(String(200))
    id_produttore = Column(Integer, ForeignKey("produttori_cavi.id_produttore"), nullable=True)
    id_categoria = Column(Integer, ForeignKey("categorie_cavi.id_categoria"), nullable=False)
    id_standard_principale = Column(Integer, ForeignKey("standard_cavi.id_standard"), nullable=True)
    descrizione_breve = Column(Text)
    descrizione_completa = Column(Text)
    materiale_guaina_esterna = Column(String(50))
    diametro_esterno_mm = Column(DECIMAL(8, 2))
    peso_kg_per_km = Column(DECIMAL(10, 2))
    temperatura_min_celsius = Column(Integer)
    temperatura_max_celsius = Column(Integer)
    raggio_curvatura_min_mm = Column(DECIMAL(8, 2))
    resistente_uv = Column(Boolean, default=False)
    resistente_olio = Column(Boolean, default=False)
    resistente_fiamma = Column(Boolean, default=False)
    per_esterno = Column(Boolean, default=False)
    per_interrato = Column(Boolean, default=False)
    scheda_tecnica_url = Column(String(300))
    immagine_url = Column(String(300))
    prezzo_indicativo_euro_per_metro = Column(DECIMAL(10, 4))
    disponibile = Column(Boolean, default=True)
    note = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Vincoli di unicità
    __table_args__ = (
        UniqueConstraint('codice_prodotto', 'id_produttore', name='uq_codice_produttore'),
    )

    # Relazioni
    produttore = relationship("ProduttoreCavo", back_populates="tipologie")
    categoria = relationship("CategoriaCavo", back_populates="tipologie")
    standard_principale = relationship("StandardCavo", foreign_keys=[id_standard_principale])
    specifiche_tecniche = relationship("SpecificaTecnicaCavo", back_populates="tipologia", cascade="all, delete-orphan")
    tipologie_standard = relationship("TipologiaStandard", back_populates="tipologia", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<TipologiaCavo(id={self.id_tipologia}, codice='{self.codice_prodotto}', nome='{self.nome_commerciale}')>"


class SpecificaTecnicaCavo(Base):
    """Modello per le specifiche tecniche dei cavi (chiave-valore flessibile)."""
    __tablename__ = "specifiche_tecniche_cavi"

    id_specifica = Column(Integer, primary_key=True, autoincrement=True)
    id_tipologia = Column(Integer, ForeignKey("tipologie_cavi.id_tipologia"), nullable=False)
    nome_attributo = Column(String(100), nullable=False)
    valore_attributo = Column(Text, nullable=False)
    unita_misura = Column(String(20))
    tipo_dato = Column(String(20), default='text')  # text, number, boolean, date
    gruppo_attributo = Column(String(50))  # per raggruppare attributi correlati
    ordine_visualizzazione = Column(Integer, default=0)
    obbligatorio = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Vincoli di unicità
    __table_args__ = (
        UniqueConstraint('id_tipologia', 'nome_attributo', name='uq_tipologia_attributo'),
    )

    # Relazioni
    tipologia = relationship("TipologiaCavo", back_populates="specifiche_tecniche")

    def __repr__(self):
        return f"<SpecificaTecnicaCavo(id={self.id_specifica}, attributo='{self.nome_attributo}', valore='{self.valore_attributo}')>"


class TipologiaStandard(Base):
    """Modello per la relazione molti-a-molti tra tipologie e standard."""
    __tablename__ = "tipologie_standard"

    id_tipologia = Column(Integer, ForeignKey("tipologie_cavi.id_tipologia"), primary_key=True)
    id_standard = Column(Integer, ForeignKey("standard_cavi.id_standard"), primary_key=True)
    conforme = Column(Boolean, default=True)
    note_conformita = Column(Text)
    data_certificazione = Column(DateTime)
    created_at = Column(DateTime, default=func.now())

    # Relazioni
    tipologia = relationship("TipologiaCavo", back_populates="tipologie_standard")
    standard = relationship("StandardCavo", back_populates="tipologie_standard")

    def __repr__(self):
        return f"<TipologiaStandard(tipologia_id={self.id_tipologia}, standard_id={self.id_standard}, conforme={self.conforme})>"
