{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\TipologieCaviManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Tabs, Tab, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Chip, IconButton, Alert, CircularProgress, Grid, Card, CardContent, CardActions, Accordion, AccordionSummary, AccordionDetails, FormControlLabel, Switch } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, ExpandMore as ExpandMoreIcon, Category as CategoryIcon, Business as BusinessIcon, Assignment as AssignmentIcon, Cable as CableIcon, Search as SearchIcon } from '@mui/icons-material';\nimport tipologieCaviService from '../../services/tipologieCaviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TipologieCaviManager = () => {\n  _s();\n  const [tabValue, setTabValue] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per categorie\n  const [categorie, setCategorie] = useState([]);\n  const [categoriaDialog, setCategoriaDialog] = useState(false);\n  const [categoriaForm, setCategoriaForm] = useState({\n    nome_categoria: '',\n    descrizione: '',\n    id_categoria_padre: null,\n    livello: 1,\n    ordine_visualizzazione: 0,\n    attiva: true\n  });\n  const [editingCategoria, setEditingCategoria] = useState(null);\n\n  // Stati per produttori\n  const [produttori, setProduttori] = useState([]);\n  const [produttoreDialog, setProduttoreDialog] = useState(false);\n  const [produttoreForm, setProduttoreForm] = useState({\n    nome_produttore: '',\n    paese: '',\n    sito_web: '',\n    email_contatto: '',\n    telefono: '',\n    note: '',\n    attivo: true\n  });\n  const [editingProduttore, setEditingProduttore] = useState(null);\n\n  // Stati per standard\n  const [standard, setStandard] = useState([]);\n  const [standardDialog, setStandardDialog] = useState(false);\n  const [standardForm, setStandardForm] = useState({\n    nome_standard: '',\n    ente_normativo: '',\n    descrizione: '',\n    anno_pubblicazione: null,\n    versione: '',\n    url_documento: '',\n    attivo: true\n  });\n  const [editingStandard, setEditingStandard] = useState(null);\n\n  // Stati per tipologie\n  const [tipologie, setTipologie] = useState([]);\n  const [tipologieTotal, setTipologieTotal] = useState(0);\n  const [tipologiePage, setTipologiePage] = useState(1);\n  const [tipologiePageSize] = useState(20);\n  const [tipologiaDialog, setTipologiaDialog] = useState(false);\n  const [tipologiaForm, setTipologiaForm] = useState({\n    codice_prodotto: '',\n    nome_commerciale: '',\n    id_produttore: null,\n    id_categoria: null,\n    id_standard_principale: null,\n    descrizione_breve: '',\n    descrizione_completa: '',\n    materiale_guaina_esterna: '',\n    diametro_esterno_mm: null,\n    peso_kg_per_km: null,\n    temperatura_min_celsius: null,\n    temperatura_max_celsius: null,\n    raggio_curvatura_min_mm: null,\n    resistente_uv: false,\n    resistente_olio: false,\n    resistente_fiamma: false,\n    per_esterno: false,\n    per_interrato: false,\n    scheda_tecnica_url: '',\n    immagine_url: '',\n    prezzo_indicativo_euro_per_metro: null,\n    disponibile: true,\n    note: ''\n  });\n  const [editingTipologia, setEditingTipologia] = useState(null);\n\n  // Filtri per tipologie\n  const [filtriTipologie, setFiltriTipologie] = useState({\n    categoria_id: null,\n    produttore_id: null,\n    disponibile: null,\n    search_text: ''\n  });\n  useEffect(() => {\n    loadData();\n  }, [tabValue]);\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      switch (tabValue) {\n        case 0:\n          await loadCategorie();\n          break;\n        case 1:\n          await loadProduttori();\n          break;\n        case 2:\n          await loadStandard();\n          break;\n        case 3:\n          // Per le tipologie, carica anche categorie, produttori e standard\n          await Promise.all([loadTipologie(), loadCategorie(), loadProduttori(), loadStandard()]);\n          break;\n      }\n    } catch (error) {\n      setError('Errore nel caricamento dei dati: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadCategorie = async () => {\n    const data = await tipologieCaviService.getCategorie();\n    setCategorie(data);\n  };\n  const loadProduttori = async () => {\n    const data = await tipologieCaviService.getProduttori();\n    setProduttori(data);\n  };\n  const loadStandard = async () => {\n    const data = await tipologieCaviService.getStandard();\n    setStandard(data);\n  };\n  const loadTipologie = async () => {\n    const params = {\n      page: tipologiePage,\n      page_size: tipologiePageSize,\n      ...filtriTipologie\n    };\n    const data = await tipologieCaviService.getTipologie(params);\n    setTipologie(data.tipologie);\n    setTipologieTotal(data.total_count);\n  };\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Funzioni per categorie\n  const handleCreateCategoria = () => {\n    setCategoriaForm({\n      nome_categoria: '',\n      descrizione: '',\n      id_categoria_padre: null,\n      livello: 1,\n      ordine_visualizzazione: 0,\n      attiva: true\n    });\n    setEditingCategoria(null);\n    setCategoriaDialog(true);\n  };\n  const handleEditCategoria = categoria => {\n    setCategoriaForm(categoria);\n    setEditingCategoria(categoria.id_categoria);\n    setCategoriaDialog(true);\n  };\n  const handleSaveCategoria = async () => {\n    try {\n      if (editingCategoria) {\n        await tipologieCaviService.updateCategoria(editingCategoria, categoriaForm);\n        setSuccess('Categoria aggiornata con successo');\n      } else {\n        await tipologieCaviService.createCategoria(categoriaForm);\n        setSuccess('Categoria creata con successo');\n      }\n      setCategoriaDialog(false);\n      await loadCategorie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteCategoria = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questa categoria?')) {\n      try {\n        await tipologieCaviService.deleteCategoria(id);\n        setSuccess('Categoria eliminata con successo');\n        await loadCategorie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per produttori\n  const handleCreateProduttore = () => {\n    setProduttoreForm({\n      nome_produttore: '',\n      paese: '',\n      sito_web: '',\n      email_contatto: '',\n      telefono: '',\n      note: '',\n      attivo: true\n    });\n    setEditingProduttore(null);\n    setProduttoreDialog(true);\n  };\n  const handleEditProduttore = produttore => {\n    setProduttoreForm(produttore);\n    setEditingProduttore(produttore.id_produttore);\n    setProduttoreDialog(true);\n  };\n  const handleSaveProduttore = async () => {\n    try {\n      if (editingProduttore) {\n        await tipologieCaviService.updateProduttore(editingProduttore, produttoreForm);\n        setSuccess('Produttore aggiornato con successo');\n      } else {\n        await tipologieCaviService.createProduttore(produttoreForm);\n        setSuccess('Produttore creato con successo');\n      }\n      setProduttoreDialog(false);\n      await loadProduttori();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteProduttore = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questo produttore?')) {\n      try {\n        await tipologieCaviService.deleteProduttore(id);\n        setSuccess('Produttore eliminato con successo');\n        await loadProduttori();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per tipologie\n  const handleCreateTipologia = () => {\n    setTipologiaForm({\n      codice_prodotto: '',\n      nome_commerciale: '',\n      id_produttore: null,\n      id_categoria: null,\n      id_standard_principale: null,\n      descrizione_breve: '',\n      descrizione_completa: '',\n      materiale_guaina_esterna: '',\n      diametro_esterno_mm: null,\n      peso_kg_per_km: null,\n      temperatura_min_celsius: null,\n      temperatura_max_celsius: null,\n      raggio_curvatura_min_mm: null,\n      resistente_uv: false,\n      resistente_olio: false,\n      resistente_fiamma: false,\n      per_esterno: false,\n      per_interrato: false,\n      scheda_tecnica_url: '',\n      immagine_url: '',\n      prezzo_indicativo_euro_per_metro: null,\n      disponibile: true,\n      note: ''\n    });\n    setEditingTipologia(null);\n    setTipologiaDialog(true);\n  };\n  const handleEditTipologia = tipologia => {\n    setTipologiaForm(tipologia);\n    setEditingTipologia(tipologia.id_tipologia);\n    setTipologiaDialog(true);\n  };\n  const handleSaveTipologia = async () => {\n    try {\n      if (editingTipologia) {\n        await tipologieCaviService.updateTipologia(editingTipologia, tipologiaForm);\n        setSuccess('Tipologia aggiornata con successo');\n      } else {\n        await tipologieCaviService.createTipologia(tipologiaForm);\n        setSuccess('Tipologia creata con successo');\n      }\n      setTipologiaDialog(false);\n      await loadTipologie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteTipologia = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questa tipologia?')) {\n      try {\n        await tipologieCaviService.deleteTipologia(id);\n        setSuccess('Tipologia eliminata con successo');\n        await loadTipologie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per standard\n  const handleCreateStandard = () => {\n    setStandardForm({\n      nome_standard: '',\n      ente_normativo: '',\n      descrizione: '',\n      anno_pubblicazione: null,\n      versione: '',\n      url_documento: '',\n      attivo: true\n    });\n    setEditingStandard(null);\n    setStandardDialog(true);\n  };\n  const handleEditStandard = std => {\n    setStandardForm(std);\n    setEditingStandard(std.id_standard);\n    setStandardDialog(true);\n  };\n  const handleSaveStandard = async () => {\n    try {\n      if (editingStandard) {\n        await tipologieCaviService.updateStandard(editingStandard, standardForm);\n        setSuccess('Standard aggiornato con successo');\n      } else {\n        await tipologieCaviService.createStandard(standardForm);\n        setSuccess('Standard creato con successo');\n      }\n      setStandardDialog(false);\n      await loadStandard();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteStandard = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questo standard?')) {\n      try {\n        await tipologieCaviService.deleteStandard(id);\n        setSuccess('Standard eliminato con successo');\n        await loadStandard();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n  const renderCategorieTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Categorie Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateCategoria,\n        children: \"Nuova Categoria\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Descrizione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Livello\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Categoria Padre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: categorie.map(categoria => {\n            var _categoria$categoria_;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.nome_categoria\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.descrizione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.livello\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_categoria$categoria_ = categoria.categoria_padre) === null || _categoria$categoria_ === void 0 ? void 0 : _categoria$categoria_.nome_categoria) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: categoria.attiva ? 'Sì' : 'No',\n                  color: categoria.attiva ? 'success' : 'default',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleEditCategoria(categoria),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleDeleteCategoria(categoria.id_categoria),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, categoria.id_categoria, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 417,\n    columnNumber: 5\n  }, this);\n  const renderProduttoriTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Produttori Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateProduttore,\n        children: \"Nuovo Produttore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Paese\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Telefono\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: produttori.map(produttore => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.nome_produttore\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.paese\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.email_contatto\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.telefono\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: produttore.attivo ? 'Sì' : 'No',\n                color: produttore.attivo ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleEditProduttore(produttore),\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDeleteProduttore(produttore.id_produttore),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 19\n            }, this)]\n          }, produttore.id_produttore, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n  const renderStandardTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Standard Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateStandard,\n        children: \"Nuovo Standard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ente Normativo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Anno\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Versione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: standard.map(std => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.nome_standard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.ente_normativo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.anno_pubblicazione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.versione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: std.attivo ? 'Sì' : 'No',\n                color: std.attivo ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleEditStandard(std),\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDeleteStandard(std.id_standard),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this)]\n          }, std.id_standard, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 537,\n    columnNumber: 5\n  }, this);\n  const renderTipologieTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Tipologie Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateTipologia,\n        children: \"Nuova Tipologia\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Ricerca\",\n            value: filtriTipologie.search_text,\n            onChange: e => setFiltriTipologie({\n              ...filtriTipologie,\n              search_text: e.target.value\n            }),\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 33\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Categoria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filtriTipologie.categoria_id || '',\n              onChange: e => setFiltriTipologie({\n                ...filtriTipologie,\n                categoria_id: e.target.value || null\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutte\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), categorie.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: cat.id_categoria,\n                children: cat.nome_categoria\n              }, cat.id_categoria, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Produttore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filtriTipologie.produttore_id || '',\n              onChange: e => setFiltriTipologie({\n                ...filtriTipologie,\n                produttore_id: e.target.value || null\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this), produttori.map(prod => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: prod.id_produttore,\n                children: prod.nome_produttore\n              }, prod.id_produttore, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            fullWidth: true,\n            onClick: loadTipologie,\n            sx: {\n              height: '56px'\n            },\n            children: \"Applica Filtri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice Prodotto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome Commerciale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Produttore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Categoria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: tipologie.map(tipologia => {\n            var _tipologia$produttore, _tipologia$categoria;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: tipologia.codice_prodotto\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: tipologia.nome_commerciale\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_tipologia$produttore = tipologia.produttore) === null || _tipologia$produttore === void 0 ? void 0 : _tipologia$produttore.nome_produttore) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_tipologia$categoria = tipologia.categoria) === null || _tipologia$categoria === void 0 ? void 0 : _tipologia$categoria.nome_categoria) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: tipologia.disponibile ? 'Sì' : 'No',\n                  color: tipologia.disponibile ? 'success' : 'default',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleEditTipologia(tipologia),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleDeleteTipologia(tipologia.id_tipologia),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this)]\n            }, tipologia.id_tipologia, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 596,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setError(''),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"scrollable\",\n        scrollButtons: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 22\n          }, this),\n          label: \"Categorie\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 22\n          }, this),\n          label: \"Produttori\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 22\n          }, this),\n          label: \"Standard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 22\n          }, this),\n          label: \"Tipologie\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [tabValue === 0 && renderCategorieTab(), tabValue === 1 && renderProduttoriTab(), tabValue === 2 && renderStandardTab(), tabValue === 3 && renderTipologieTab()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: categoriaDialog,\n      onClose: () => setCategoriaDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingCategoria ? 'Modifica Categoria' : 'Nuova Categoria'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 752,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Categoria\",\n              value: categoriaForm.nome_categoria,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                nome_categoria: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Categoria Padre\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: categoriaForm.id_categoria_padre || '',\n                onChange: e => setCategoriaForm({\n                  ...categoriaForm,\n                  id_categoria_padre: e.target.value || null\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Nessuna (Categoria principale)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 19\n                }, this), categorie.filter(c => c.livello < 3).map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat.id_categoria,\n                  children: cat.nome_categoria\n                }, cat.id_categoria, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: categoriaForm.descrizione,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                descrizione: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Ordine Visualizzazione\",\n              type: \"number\",\n              value: categoriaForm.ordine_visualizzazione,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                ordine_visualizzazione: parseInt(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: categoriaForm.attiva,\n                onChange: e => setCategoriaForm({\n                  ...categoriaForm,\n                  attiva: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 19\n              }, this),\n              label: \"Categoria Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCategoriaDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveCategoria,\n          variant: \"contained\",\n          children: editingCategoria ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 814,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 751,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: produttoreDialog,\n      onClose: () => setProduttoreDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingProduttore ? 'Modifica Produttore' : 'Nuovo Produttore'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Produttore\",\n              value: produttoreForm.nome_produttore,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                nome_produttore: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Paese\",\n              value: produttoreForm.paese,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                paese: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Contatto\",\n              type: \"email\",\n              value: produttoreForm.email_contatto,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                email_contatto: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Telefono\",\n              value: produttoreForm.telefono,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                telefono: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Sito Web\",\n              value: produttoreForm.sito_web,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                sito_web: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              value: produttoreForm.note,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                note: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: produttoreForm.attivo,\n                onChange: e => setProduttoreForm({\n                  ...produttoreForm,\n                  attivo: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 19\n              }, this),\n              label: \"Produttore Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setProduttoreDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveProduttore,\n          variant: \"contained\",\n          children: editingProduttore ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: standardDialog,\n      onClose: () => setStandardDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingStandard ? 'Modifica Standard' : 'Nuovo Standard'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Standard\",\n              value: standardForm.nome_standard,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                nome_standard: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Ente Normativo\",\n              value: standardForm.ente_normativo,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                ente_normativo: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: standardForm.descrizione,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                descrizione: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Anno Pubblicazione\",\n              type: \"number\",\n              value: standardForm.anno_pubblicazione || '',\n              onChange: e => setStandardForm({\n                ...standardForm,\n                anno_pubblicazione: parseInt(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Versione\",\n              value: standardForm.versione,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                versione: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: standardForm.attivo,\n                onChange: e => setStandardForm({\n                  ...standardForm,\n                  attivo: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 19\n              }, this),\n              label: \"Standard Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"URL Documento\",\n              value: standardForm.url_documento,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                url_documento: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setStandardDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveStandard,\n          variant: \"contained\",\n          children: editingStandard ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 903,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: tipologiaDialog,\n      onClose: () => setTipologiaDialog(false),\n      maxWidth: \"lg\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingTipologia ? 'Modifica Tipologia' : 'Nuova Tipologia'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 984,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Codice Prodotto\",\n              value: tipologiaForm.codice_prodotto,\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                codice_prodotto: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Commerciale\",\n              value: tipologiaForm.nome_commerciale,\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                nome_commerciale: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Categoria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: tipologiaForm.id_categoria || '',\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  id_categoria: e.target.value || null\n                }),\n                children: categorie.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat.id_categoria,\n                  children: cat.nome_categoria\n                }, cat.id_categoria, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Produttore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: tipologiaForm.id_produttore || '',\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  id_produttore: e.target.value || null\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Nessuno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 19\n                }, this), produttori.map(prod => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: prod.id_produttore,\n                  children: prod.nome_produttore\n                }, prod.id_produttore, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1021,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Standard Principale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: tipologiaForm.id_standard_principale || '',\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  id_standard_principale: e.target.value || null\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Nessuno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 19\n                }, this), standard.map(std => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: std.id_standard,\n                  children: std.nome_standard\n                }, std.id_standard, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione Breve\",\n              value: tipologiaForm.descrizione_breve,\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                descrizione_breve: e.target.value\n              }),\n              multiline: true,\n              rows: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Materiale Guaina Esterna\",\n              value: tipologiaForm.materiale_guaina_esterna,\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                materiale_guaina_esterna: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Diametro Esterno (mm)\",\n              type: \"number\",\n              value: tipologiaForm.diametro_esterno_mm || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                diametro_esterno_mm: parseFloat(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Peso (kg/km)\",\n              type: \"number\",\n              value: tipologiaForm.peso_kg_per_km || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                peso_kg_per_km: parseFloat(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temp. Min (\\xB0C)\",\n              type: \"number\",\n              value: tipologiaForm.temperatura_min_celsius || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                temperatura_min_celsius: parseInt(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temp. Max (\\xB0C)\",\n              type: \"number\",\n              value: tipologiaForm.temperatura_max_celsius || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                temperatura_max_celsius: parseInt(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1099,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Prezzo Indicativo (\\u20AC/m)\",\n              type: \"number\",\n              step: \"0.0001\",\n              value: tipologiaForm.prezzo_indicativo_euro_per_metro || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                prezzo_indicativo_euro_per_metro: parseFloat(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Raggio Curvatura Min (mm)\",\n              type: \"number\",\n              value: tipologiaForm.raggio_curvatura_min_mm || '',\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                raggio_curvatura_min_mm: parseFloat(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.resistente_uv,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  resistente_uv: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 19\n              }, this),\n              label: \"Resistente UV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.resistente_olio,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  resistente_olio: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1140,\n                columnNumber: 19\n              }, this),\n              label: \"Resistente Olio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.resistente_fiamma,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  resistente_fiamma: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 19\n              }, this),\n              label: \"Resistente Fiamma\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.disponibile,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  disponibile: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 19\n              }, this),\n              label: \"Disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.per_esterno,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  per_esterno: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1173,\n                columnNumber: 19\n              }, this),\n              label: \"Per Esterno\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: tipologiaForm.per_interrato,\n                onChange: e => setTipologiaForm({\n                  ...tipologiaForm,\n                  per_interrato: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 19\n              }, this),\n              label: \"Per Interrato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              value: tipologiaForm.note,\n              onChange: e => setTipologiaForm({\n                ...tipologiaForm,\n                note: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setTipologiaDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveTipologia,\n          variant: \"contained\",\n          children: editingTipologia ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 983,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 714,\n    columnNumber: 5\n  }, this);\n};\n_s(TipologieCaviManager, \"iDQG0UQ86/1tKvQ+BcBsV3pOgHw=\");\n_c = TipologieCaviManager;\nexport default TipologieCaviManager;\nvar _c;\n$RefreshReg$(_c, \"TipologieCaviManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Tabs", "Tab", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "IconButton", "<PERSON><PERSON>", "CircularProgress", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Accordion", "AccordionSummary", "AccordionDetails", "FormControlLabel", "Switch", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "ExpandMore", "ExpandMoreIcon", "Category", "CategoryIcon", "Business", "BusinessIcon", "Assignment", "AssignmentIcon", "Cable", "CableIcon", "Search", "SearchIcon", "tipologieCaviService", "jsxDEV", "_jsxDEV", "TipologieCaviManager", "_s", "tabValue", "setTabValue", "loading", "setLoading", "error", "setError", "success", "setSuccess", "categorie", "setCategorie", "categoriaDialog", "setCategoriaDialog", "categoriaForm", "setCategoriaForm", "nome_categoria", "descrizione", "id_categoria_padre", "livello", "ordine_visualizzazione", "attiva", "editingCategoria", "setEditingCategoria", "produttori", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "produttoreDialog", "setProduttoreDialog", "produttoreForm", "setProduttoreForm", "nome_produttore", "paese", "sito_web", "email_contatto", "telefono", "note", "attivo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditingProduttore", "standard", "setStandard", "standardDialog", "setStandardDialog", "standardForm", "setStandardForm", "nome_standard", "ente_normativo", "anno_pubblicazione", "versione", "url_documento", "editingStandard", "setEditingStandard", "tipologie", "setTipologie", "tipologieTotal", "setTipologieTotal", "tipologiePage", "setTipologiePage", "tipologiePageSize", "tipologiaDialog", "setTipologiaDialog", "tipologiaForm", "setTipologiaForm", "codice_prodotto", "nome_commerciale", "id_produttore", "id_categoria", "id_standard_principale", "descrizione_breve", "descrizione_completa", "materiale_guaina_esterna", "diametro_esterno_mm", "peso_kg_per_km", "temperatura_min_celsius", "temperatura_max_celsius", "raggio_curvatura_min_mm", "resistente_uv", "resistente_olio", "resistente_fiamma", "per_esterno", "per_interrato", "scheda_tecnica_url", "immagine_url", "prezzo_indicativo_euro_per_metro", "disponibile", "editingTipologia", "setEditingTipologia", "filtriTipologie", "setFiltriTipologie", "categoria_id", "produttore_id", "search_text", "loadData", "loadCategorie", "loadProduttori", "loadStandard", "Promise", "all", "loadTipologie", "message", "data", "getCategorie", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getStandard", "params", "page", "page_size", "getTipologie", "total_count", "handleTabChange", "event", "newValue", "handleCreateCategoria", "handleEditCategoria", "categoria", "handleSaveCategoria", "updateCategoria", "createCategoria", "handleDeleteCategoria", "id", "window", "confirm", "deleteCategoria", "handleCreateProduttore", "handleEditProduttore", "produttore", "handleSaveProduttore", "updateProduttore", "createProduttore", "handleDeleteProduttore", "deleteProduttore", "handleCreateTipologia", "handleEditTipologia", "tipologia", "id_tipologia", "handleSaveTipologia", "updateTipologia", "createTipologia", "handleDeleteTipologia", "deleteTipologia", "handleCreateStandard", "handleEditStandard", "std", "id_standard", "handleSaveStandard", "updateStandard", "createStandard", "handleDeleteStandard", "deleteStandard", "renderCategorieTab", "children", "sx", "display", "justifyContent", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "component", "map", "_categoria$categoria_", "categoria_padre", "label", "color", "size", "renderProduttoriTab", "renderStandardTab", "renderTipologieTab", "p", "container", "spacing", "item", "xs", "md", "fullWidth", "value", "onChange", "e", "target", "InputProps", "startAdornment", "cat", "prod", "height", "_tipologia$produttore", "_tipologia$categoria", "width", "severity", "onClose", "indicatorColor", "textColor", "scrollButtons", "icon", "mt", "open", "max<PERSON><PERSON><PERSON>", "required", "filter", "c", "multiline", "rows", "type", "parseInt", "control", "checked", "parseFloat", "step", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/admin/TipologieCaviManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Tabs,\n  Tab,\n  Typography,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  IconButton,\n  Alert,\n  CircularProgress,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  FormControlLabel,\n  Switch\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  ExpandMore as ExpandMoreIcon,\n  Category as CategoryIcon,\n  Business as BusinessIcon,\n  Assignment as AssignmentIcon,\n  Cable as CableIcon,\n  Search as SearchIcon\n} from '@mui/icons-material';\nimport tipologieCaviService from '../../services/tipologieCaviService';\n\nconst TipologieCaviManager = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per categorie\n  const [categorie, setCategorie] = useState([]);\n  const [categoriaDialog, setCategoriaDialog] = useState(false);\n  const [categoriaForm, setCategoriaForm] = useState({\n    nome_categoria: '',\n    descrizione: '',\n    id_categoria_padre: null,\n    livello: 1,\n    ordine_visualizzazione: 0,\n    attiva: true\n  });\n  const [editingCategoria, setEditingCategoria] = useState(null);\n\n  // Stati per produttori\n  const [produttori, setProduttori] = useState([]);\n  const [produttoreDialog, setProduttoreDialog] = useState(false);\n  const [produttoreForm, setProduttoreForm] = useState({\n    nome_produttore: '',\n    paese: '',\n    sito_web: '',\n    email_contatto: '',\n    telefono: '',\n    note: '',\n    attivo: true\n  });\n  const [editingProduttore, setEditingProduttore] = useState(null);\n\n  // Stati per standard\n  const [standard, setStandard] = useState([]);\n  const [standardDialog, setStandardDialog] = useState(false);\n  const [standardForm, setStandardForm] = useState({\n    nome_standard: '',\n    ente_normativo: '',\n    descrizione: '',\n    anno_pubblicazione: null,\n    versione: '',\n    url_documento: '',\n    attivo: true\n  });\n  const [editingStandard, setEditingStandard] = useState(null);\n\n  // Stati per tipologie\n  const [tipologie, setTipologie] = useState([]);\n  const [tipologieTotal, setTipologieTotal] = useState(0);\n  const [tipologiePage, setTipologiePage] = useState(1);\n  const [tipologiePageSize] = useState(20);\n  const [tipologiaDialog, setTipologiaDialog] = useState(false);\n  const [tipologiaForm, setTipologiaForm] = useState({\n    codice_prodotto: '',\n    nome_commerciale: '',\n    id_produttore: null,\n    id_categoria: null,\n    id_standard_principale: null,\n    descrizione_breve: '',\n    descrizione_completa: '',\n    materiale_guaina_esterna: '',\n    diametro_esterno_mm: null,\n    peso_kg_per_km: null,\n    temperatura_min_celsius: null,\n    temperatura_max_celsius: null,\n    raggio_curvatura_min_mm: null,\n    resistente_uv: false,\n    resistente_olio: false,\n    resistente_fiamma: false,\n    per_esterno: false,\n    per_interrato: false,\n    scheda_tecnica_url: '',\n    immagine_url: '',\n    prezzo_indicativo_euro_per_metro: null,\n    disponibile: true,\n    note: ''\n  });\n  const [editingTipologia, setEditingTipologia] = useState(null);\n\n  // Filtri per tipologie\n  const [filtriTipologie, setFiltriTipologie] = useState({\n    categoria_id: null,\n    produttore_id: null,\n    disponibile: null,\n    search_text: ''\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [tabValue]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      switch (tabValue) {\n        case 0:\n          await loadCategorie();\n          break;\n        case 1:\n          await loadProduttori();\n          break;\n        case 2:\n          await loadStandard();\n          break;\n        case 3:\n          // Per le tipologie, carica anche categorie, produttori e standard\n          await Promise.all([\n            loadTipologie(),\n            loadCategorie(),\n            loadProduttori(),\n            loadStandard()\n          ]);\n          break;\n      }\n    } catch (error) {\n      setError('Errore nel caricamento dei dati: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategorie = async () => {\n    const data = await tipologieCaviService.getCategorie();\n    setCategorie(data);\n  };\n\n  const loadProduttori = async () => {\n    const data = await tipologieCaviService.getProduttori();\n    setProduttori(data);\n  };\n\n  const loadStandard = async () => {\n    const data = await tipologieCaviService.getStandard();\n    setStandard(data);\n  };\n\n  const loadTipologie = async () => {\n    const params = {\n      page: tipologiePage,\n      page_size: tipologiePageSize,\n      ...filtriTipologie\n    };\n    const data = await tipologieCaviService.getTipologie(params);\n    setTipologie(data.tipologie);\n    setTipologieTotal(data.total_count);\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Funzioni per categorie\n  const handleCreateCategoria = () => {\n    setCategoriaForm({\n      nome_categoria: '',\n      descrizione: '',\n      id_categoria_padre: null,\n      livello: 1,\n      ordine_visualizzazione: 0,\n      attiva: true\n    });\n    setEditingCategoria(null);\n    setCategoriaDialog(true);\n  };\n\n  const handleEditCategoria = (categoria) => {\n    setCategoriaForm(categoria);\n    setEditingCategoria(categoria.id_categoria);\n    setCategoriaDialog(true);\n  };\n\n  const handleSaveCategoria = async () => {\n    try {\n      if (editingCategoria) {\n        await tipologieCaviService.updateCategoria(editingCategoria, categoriaForm);\n        setSuccess('Categoria aggiornata con successo');\n      } else {\n        await tipologieCaviService.createCategoria(categoriaForm);\n        setSuccess('Categoria creata con successo');\n      }\n      setCategoriaDialog(false);\n      await loadCategorie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteCategoria = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa categoria?')) {\n      try {\n        await tipologieCaviService.deleteCategoria(id);\n        setSuccess('Categoria eliminata con successo');\n        await loadCategorie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per produttori\n  const handleCreateProduttore = () => {\n    setProduttoreForm({\n      nome_produttore: '',\n      paese: '',\n      sito_web: '',\n      email_contatto: '',\n      telefono: '',\n      note: '',\n      attivo: true\n    });\n    setEditingProduttore(null);\n    setProduttoreDialog(true);\n  };\n\n  const handleEditProduttore = (produttore) => {\n    setProduttoreForm(produttore);\n    setEditingProduttore(produttore.id_produttore);\n    setProduttoreDialog(true);\n  };\n\n  const handleSaveProduttore = async () => {\n    try {\n      if (editingProduttore) {\n        await tipologieCaviService.updateProduttore(editingProduttore, produttoreForm);\n        setSuccess('Produttore aggiornato con successo');\n      } else {\n        await tipologieCaviService.createProduttore(produttoreForm);\n        setSuccess('Produttore creato con successo');\n      }\n      setProduttoreDialog(false);\n      await loadProduttori();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteProduttore = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questo produttore?')) {\n      try {\n        await tipologieCaviService.deleteProduttore(id);\n        setSuccess('Produttore eliminato con successo');\n        await loadProduttori();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per tipologie\n  const handleCreateTipologia = () => {\n    setTipologiaForm({\n      codice_prodotto: '',\n      nome_commerciale: '',\n      id_produttore: null,\n      id_categoria: null,\n      id_standard_principale: null,\n      descrizione_breve: '',\n      descrizione_completa: '',\n      materiale_guaina_esterna: '',\n      diametro_esterno_mm: null,\n      peso_kg_per_km: null,\n      temperatura_min_celsius: null,\n      temperatura_max_celsius: null,\n      raggio_curvatura_min_mm: null,\n      resistente_uv: false,\n      resistente_olio: false,\n      resistente_fiamma: false,\n      per_esterno: false,\n      per_interrato: false,\n      scheda_tecnica_url: '',\n      immagine_url: '',\n      prezzo_indicativo_euro_per_metro: null,\n      disponibile: true,\n      note: ''\n    });\n    setEditingTipologia(null);\n    setTipologiaDialog(true);\n  };\n\n  const handleEditTipologia = (tipologia) => {\n    setTipologiaForm(tipologia);\n    setEditingTipologia(tipologia.id_tipologia);\n    setTipologiaDialog(true);\n  };\n\n  const handleSaveTipologia = async () => {\n    try {\n      if (editingTipologia) {\n        await tipologieCaviService.updateTipologia(editingTipologia, tipologiaForm);\n        setSuccess('Tipologia aggiornata con successo');\n      } else {\n        await tipologieCaviService.createTipologia(tipologiaForm);\n        setSuccess('Tipologia creata con successo');\n      }\n      setTipologiaDialog(false);\n      await loadTipologie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteTipologia = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa tipologia?')) {\n      try {\n        await tipologieCaviService.deleteTipologia(id);\n        setSuccess('Tipologia eliminata con successo');\n        await loadTipologie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per standard\n  const handleCreateStandard = () => {\n    setStandardForm({\n      nome_standard: '',\n      ente_normativo: '',\n      descrizione: '',\n      anno_pubblicazione: null,\n      versione: '',\n      url_documento: '',\n      attivo: true\n    });\n    setEditingStandard(null);\n    setStandardDialog(true);\n  };\n\n  const handleEditStandard = (std) => {\n    setStandardForm(std);\n    setEditingStandard(std.id_standard);\n    setStandardDialog(true);\n  };\n\n  const handleSaveStandard = async () => {\n    try {\n      if (editingStandard) {\n        await tipologieCaviService.updateStandard(editingStandard, standardForm);\n        setSuccess('Standard aggiornato con successo');\n      } else {\n        await tipologieCaviService.createStandard(standardForm);\n        setSuccess('Standard creato con successo');\n      }\n      setStandardDialog(false);\n      await loadStandard();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteStandard = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questo standard?')) {\n      try {\n        await tipologieCaviService.deleteStandard(id);\n        setSuccess('Standard eliminato con successo');\n        await loadStandard();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  const renderCategorieTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Categorie Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateCategoria}\n        >\n          Nuova Categoria\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Descrizione</TableCell>\n                <TableCell>Livello</TableCell>\n                <TableCell>Categoria Padre</TableCell>\n                <TableCell>Attiva</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {categorie.map((categoria) => (\n                <TableRow key={categoria.id_categoria}>\n                  <TableCell>{categoria.nome_categoria}</TableCell>\n                  <TableCell>{categoria.descrizione}</TableCell>\n                  <TableCell>{categoria.livello}</TableCell>\n                  <TableCell>\n                    {categoria.categoria_padre?.nome_categoria || '-'}\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={categoria.attiva ? 'Sì' : 'No'}\n                      color={categoria.attiva ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditCategoria(categoria)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteCategoria(categoria.id_categoria)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderProduttoriTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Produttori Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateProduttore}\n        >\n          Nuovo Produttore\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Paese</TableCell>\n                <TableCell>Email</TableCell>\n                <TableCell>Telefono</TableCell>\n                <TableCell>Attivo</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {produttori.map((produttore) => (\n                <TableRow key={produttore.id_produttore}>\n                  <TableCell>{produttore.nome_produttore}</TableCell>\n                  <TableCell>{produttore.paese}</TableCell>\n                  <TableCell>{produttore.email_contatto}</TableCell>\n                  <TableCell>{produttore.telefono}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={produttore.attivo ? 'Sì' : 'No'}\n                      color={produttore.attivo ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditProduttore(produttore)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteProduttore(produttore.id_produttore)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderStandardTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Standard Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateStandard}\n        >\n          Nuovo Standard\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Ente Normativo</TableCell>\n                <TableCell>Anno</TableCell>\n                <TableCell>Versione</TableCell>\n                <TableCell>Attivo</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {standard.map((std) => (\n                <TableRow key={std.id_standard}>\n                  <TableCell>{std.nome_standard}</TableCell>\n                  <TableCell>{std.ente_normativo}</TableCell>\n                  <TableCell>{std.anno_pubblicazione}</TableCell>\n                  <TableCell>{std.versione}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={std.attivo ? 'Sì' : 'No'}\n                      color={std.attivo ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditStandard(std)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteStandard(std.id_standard)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderTipologieTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Tipologie Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateTipologia}\n        >\n          Nuova Tipologia\n        </Button>\n      </Box>\n\n      {/* Filtri */}\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={3}>\n            <TextField\n              fullWidth\n              label=\"Ricerca\"\n              value={filtriTipologie.search_text}\n              onChange={(e) => setFiltriTipologie({...filtriTipologie, search_text: e.target.value})}\n              InputProps={{\n                startAdornment: <SearchIcon />\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel>Categoria</InputLabel>\n              <Select\n                value={filtriTipologie.categoria_id || ''}\n                onChange={(e) => setFiltriTipologie({...filtriTipologie, categoria_id: e.target.value || null})}\n              >\n                <MenuItem value=\"\">Tutte</MenuItem>\n                {categorie.map((cat) => (\n                  <MenuItem key={cat.id_categoria} value={cat.id_categoria}>\n                    {cat.nome_categoria}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel>Produttore</InputLabel>\n              <Select\n                value={filtriTipologie.produttore_id || ''}\n                onChange={(e) => setFiltriTipologie({...filtriTipologie, produttore_id: e.target.value || null})}\n              >\n                <MenuItem value=\"\">Tutti</MenuItem>\n                {produttori.map((prod) => (\n                  <MenuItem key={prod.id_produttore} value={prod.id_produttore}>\n                    {prod.nome_produttore}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Button\n              variant=\"outlined\"\n              fullWidth\n              onClick={loadTipologie}\n              sx={{ height: '56px' }}\n            >\n              Applica Filtri\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Codice Prodotto</TableCell>\n                <TableCell>Nome Commerciale</TableCell>\n                <TableCell>Produttore</TableCell>\n                <TableCell>Categoria</TableCell>\n                <TableCell>Disponibile</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {tipologie.map((tipologia) => (\n                <TableRow key={tipologia.id_tipologia}>\n                  <TableCell>{tipologia.codice_prodotto}</TableCell>\n                  <TableCell>{tipologia.nome_commerciale}</TableCell>\n                  <TableCell>{tipologia.produttore?.nome_produttore || '-'}</TableCell>\n                  <TableCell>{tipologia.categoria?.nome_categoria || '-'}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={tipologia.disponibile ? 'Sì' : 'No'}\n                      color={tipologia.disponibile ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditTipologia(tipologia)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteTipologia(tipologia.id_tipologia)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  return (\n    <Box sx={{ width: '100%' }}>\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError('')}>\n          {error}\n        </Alert>\n      )}\n\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 2 }} onClose={() => setSuccess('')}>\n          {success}\n        </Alert>\n      )}\n\n      <Paper sx={{ width: '100%', mb: 2 }}>\n        <Tabs\n          value={tabValue}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"scrollable\"\n          scrollButtons=\"auto\"\n        >\n          <Tab icon={<CategoryIcon />} label=\"Categorie\" />\n          <Tab icon={<BusinessIcon />} label=\"Produttori\" />\n          <Tab icon={<AssignmentIcon />} label=\"Standard\" />\n          <Tab icon={<CableIcon />} label=\"Tipologie\" />\n        </Tabs>\n      </Paper>\n\n      <Box sx={{ mt: 2 }}>\n        {tabValue === 0 && renderCategorieTab()}\n        {tabValue === 1 && renderProduttoriTab()}\n        {tabValue === 2 && renderStandardTab()}\n        {tabValue === 3 && renderTipologieTab()}\n      </Box>\n\n      {/* Dialog per Categoria */}\n      <Dialog open={categoriaDialog} onClose={() => setCategoriaDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingCategoria ? 'Modifica Categoria' : 'Nuova Categoria'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Categoria\"\n                value={categoriaForm.nome_categoria}\n                onChange={(e) => setCategoriaForm({...categoriaForm, nome_categoria: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Categoria Padre</InputLabel>\n                <Select\n                  value={categoriaForm.id_categoria_padre || ''}\n                  onChange={(e) => setCategoriaForm({...categoriaForm, id_categoria_padre: e.target.value || null})}\n                >\n                  <MenuItem value=\"\">Nessuna (Categoria principale)</MenuItem>\n                  {categorie.filter(c => c.livello < 3).map((cat) => (\n                    <MenuItem key={cat.id_categoria} value={cat.id_categoria}>\n                      {cat.nome_categoria}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Descrizione\"\n                value={categoriaForm.descrizione}\n                onChange={(e) => setCategoriaForm({...categoriaForm, descrizione: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Ordine Visualizzazione\"\n                type=\"number\"\n                value={categoriaForm.ordine_visualizzazione}\n                onChange={(e) => setCategoriaForm({...categoriaForm, ordine_visualizzazione: parseInt(e.target.value) || 0})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={categoriaForm.attiva}\n                    onChange={(e) => setCategoriaForm({...categoriaForm, attiva: e.target.checked})}\n                  />\n                }\n                label=\"Categoria Attiva\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setCategoriaDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveCategoria} variant=\"contained\">\n            {editingCategoria ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per Produttore */}\n      <Dialog open={produttoreDialog} onClose={() => setProduttoreDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingProduttore ? 'Modifica Produttore' : 'Nuovo Produttore'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Produttore\"\n                value={produttoreForm.nome_produttore}\n                onChange={(e) => setProduttoreForm({...produttoreForm, nome_produttore: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Paese\"\n                value={produttoreForm.paese}\n                onChange={(e) => setProduttoreForm({...produttoreForm, paese: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Email Contatto\"\n                type=\"email\"\n                value={produttoreForm.email_contatto}\n                onChange={(e) => setProduttoreForm({...produttoreForm, email_contatto: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Telefono\"\n                value={produttoreForm.telefono}\n                onChange={(e) => setProduttoreForm({...produttoreForm, telefono: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Sito Web\"\n                value={produttoreForm.sito_web}\n                onChange={(e) => setProduttoreForm({...produttoreForm, sito_web: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                value={produttoreForm.note}\n                onChange={(e) => setProduttoreForm({...produttoreForm, note: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={produttoreForm.attivo}\n                    onChange={(e) => setProduttoreForm({...produttoreForm, attivo: e.target.checked})}\n                  />\n                }\n                label=\"Produttore Attivo\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setProduttoreDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveProduttore} variant=\"contained\">\n            {editingProduttore ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per Standard */}\n      <Dialog open={standardDialog} onClose={() => setStandardDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingStandard ? 'Modifica Standard' : 'Nuovo Standard'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Standard\"\n                value={standardForm.nome_standard}\n                onChange={(e) => setStandardForm({...standardForm, nome_standard: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Ente Normativo\"\n                value={standardForm.ente_normativo}\n                onChange={(e) => setStandardForm({...standardForm, ente_normativo: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Descrizione\"\n                value={standardForm.descrizione}\n                onChange={(e) => setStandardForm({...standardForm, descrizione: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Anno Pubblicazione\"\n                type=\"number\"\n                value={standardForm.anno_pubblicazione || ''}\n                onChange={(e) => setStandardForm({...standardForm, anno_pubblicazione: parseInt(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Versione\"\n                value={standardForm.versione}\n                onChange={(e) => setStandardForm({...standardForm, versione: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={standardForm.attivo}\n                    onChange={(e) => setStandardForm({...standardForm, attivo: e.target.checked})}\n                  />\n                }\n                label=\"Standard Attivo\"\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"URL Documento\"\n                value={standardForm.url_documento}\n                onChange={(e) => setStandardForm({...standardForm, url_documento: e.target.value})}\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setStandardDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveStandard} variant=\"contained\">\n            {editingStandard ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per Tipologia */}\n      <Dialog open={tipologiaDialog} onClose={() => setTipologiaDialog(false)} maxWidth=\"lg\" fullWidth>\n        <DialogTitle>\n          {editingTipologia ? 'Modifica Tipologia' : 'Nuova Tipologia'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Codice Prodotto\"\n                value={tipologiaForm.codice_prodotto}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, codice_prodotto: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Commerciale\"\n                value={tipologiaForm.nome_commerciale}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, nome_commerciale: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth required>\n                <InputLabel>Categoria</InputLabel>\n                <Select\n                  value={tipologiaForm.id_categoria || ''}\n                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_categoria: e.target.value || null})}\n                >\n                  {categorie.map((cat) => (\n                    <MenuItem key={cat.id_categoria} value={cat.id_categoria}>\n                      {cat.nome_categoria}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Produttore</InputLabel>\n                <Select\n                  value={tipologiaForm.id_produttore || ''}\n                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_produttore: e.target.value || null})}\n                >\n                  <MenuItem value=\"\">Nessuno</MenuItem>\n                  {produttori.map((prod) => (\n                    <MenuItem key={prod.id_produttore} value={prod.id_produttore}>\n                      {prod.nome_produttore}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Standard Principale</InputLabel>\n                <Select\n                  value={tipologiaForm.id_standard_principale || ''}\n                  onChange={(e) => setTipologiaForm({...tipologiaForm, id_standard_principale: e.target.value || null})}\n                >\n                  <MenuItem value=\"\">Nessuno</MenuItem>\n                  {standard.map((std) => (\n                    <MenuItem key={std.id_standard} value={std.id_standard}>\n                      {std.nome_standard}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Descrizione Breve\"\n                value={tipologiaForm.descrizione_breve}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, descrizione_breve: e.target.value})}\n                multiline\n                rows={2}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Materiale Guaina Esterna\"\n                value={tipologiaForm.materiale_guaina_esterna}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, materiale_guaina_esterna: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Diametro Esterno (mm)\"\n                type=\"number\"\n                value={tipologiaForm.diametro_esterno_mm || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, diametro_esterno_mm: parseFloat(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Peso (kg/km)\"\n                type=\"number\"\n                value={tipologiaForm.peso_kg_per_km || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, peso_kg_per_km: parseFloat(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Temp. Min (°C)\"\n                type=\"number\"\n                value={tipologiaForm.temperatura_min_celsius || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, temperatura_min_celsius: parseInt(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Temp. Max (°C)\"\n                type=\"number\"\n                value={tipologiaForm.temperatura_max_celsius || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, temperatura_max_celsius: parseInt(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Prezzo Indicativo (€/m)\"\n                type=\"number\"\n                step=\"0.0001\"\n                value={tipologiaForm.prezzo_indicativo_euro_per_metro || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, prezzo_indicativo_euro_per_metro: parseFloat(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Raggio Curvatura Min (mm)\"\n                type=\"number\"\n                value={tipologiaForm.raggio_curvatura_min_mm || ''}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, raggio_curvatura_min_mm: parseFloat(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.resistente_uv}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_uv: e.target.checked})}\n                  />\n                }\n                label=\"Resistente UV\"\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.resistente_olio}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_olio: e.target.checked})}\n                  />\n                }\n                label=\"Resistente Olio\"\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.resistente_fiamma}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, resistente_fiamma: e.target.checked})}\n                  />\n                }\n                label=\"Resistente Fiamma\"\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.disponibile}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, disponibile: e.target.checked})}\n                  />\n                }\n                label=\"Disponibile\"\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.per_esterno}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, per_esterno: e.target.checked})}\n                  />\n                }\n                label=\"Per Esterno\"\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={tipologiaForm.per_interrato}\n                    onChange={(e) => setTipologiaForm({...tipologiaForm, per_interrato: e.target.checked})}\n                  />\n                }\n                label=\"Per Interrato\"\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                value={tipologiaForm.note}\n                onChange={(e) => setTipologiaForm({...tipologiaForm, note: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setTipologiaDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveTipologia} variant=\"contained\">\n            {editingTipologia ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default TipologieCaviManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,QACD,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,OAAOC,oBAAoB,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC;IACjDyE,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,kBAAkB,EAAE,IAAI;IACxBC,OAAO,EAAE,CAAC;IACVC,sBAAsB,EAAE,CAAC;IACzBC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC;IACnDuF,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAACgG,QAAQ,EAAEC,WAAW,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkG,cAAc,EAAEC,iBAAiB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC;IAC/CsG,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClB7B,WAAW,EAAE,EAAE;IACf8B,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBb,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+G,cAAc,EAAEC,iBAAiB,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiH,aAAa,EAAEC,gBAAgB,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACmH,iBAAiB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsH,aAAa,EAAEC,gBAAgB,CAAC,GAAGvH,QAAQ,CAAC;IACjDwH,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,sBAAsB,EAAE,IAAI;IAC5BC,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,EAAE;IACxBC,wBAAwB,EAAE,EAAE;IAC5BC,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE,IAAI;IACpBC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,iBAAiB,EAAE,KAAK;IACxBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,kBAAkB,EAAE,EAAE;IACtBC,YAAY,EAAE,EAAE;IAChBC,gCAAgC,EAAE,IAAI;IACtCC,WAAW,EAAE,IAAI;IACjBjD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACgJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGjJ,QAAQ,CAAC;IACrDkJ,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBN,WAAW,EAAE,IAAI;IACjBO,WAAW,EAAE;EACf,CAAC,CAAC;EAEFnJ,SAAS,CAAC,MAAM;IACdoJ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC1F,QAAQ,CAAC,CAAC;EAEd,MAAM0F,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BvF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,QAAQH,QAAQ;QACd,KAAK,CAAC;UACJ,MAAM2F,aAAa,CAAC,CAAC;UACrB;QACF,KAAK,CAAC;UACJ,MAAMC,cAAc,CAAC,CAAC;UACtB;QACF,KAAK,CAAC;UACJ,MAAMC,YAAY,CAAC,CAAC;UACpB;QACF,KAAK,CAAC;UACJ;UACA,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChBC,aAAa,CAAC,CAAC,EACfL,aAAa,CAAC,CAAC,EACfC,cAAc,CAAC,CAAC,EAChBC,YAAY,CAAC,CAAC,CACf,CAAC;UACF;MACJ;IACF,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdC,QAAQ,CAAC,mCAAmC,GAAGD,KAAK,CAAC6F,OAAO,CAAC;IAC/D,CAAC,SAAS;MACR9F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMO,IAAI,GAAG,MAAMvG,oBAAoB,CAACwG,YAAY,CAAC,CAAC;IACtD1F,YAAY,CAACyF,IAAI,CAAC;EACpB,CAAC;EAED,MAAMN,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMM,IAAI,GAAG,MAAMvG,oBAAoB,CAACyG,aAAa,CAAC,CAAC;IACvD7E,aAAa,CAAC2E,IAAI,CAAC;EACrB,CAAC;EAED,MAAML,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMK,IAAI,GAAG,MAAMvG,oBAAoB,CAAC0G,WAAW,CAAC,CAAC;IACrD/D,WAAW,CAAC4D,IAAI,CAAC;EACnB,CAAC;EAED,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMM,MAAM,GAAG;MACbC,IAAI,EAAEjD,aAAa;MACnBkD,SAAS,EAAEhD,iBAAiB;MAC5B,GAAG6B;IACL,CAAC;IACD,MAAMa,IAAI,GAAG,MAAMvG,oBAAoB,CAAC8G,YAAY,CAACH,MAAM,CAAC;IAC5DnD,YAAY,CAAC+C,IAAI,CAAChD,SAAS,CAAC;IAC5BG,iBAAiB,CAAC6C,IAAI,CAACQ,WAAW,CAAC;EACrC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C5G,WAAW,CAAC4G,QAAQ,CAAC;IACrBxG,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;;EAED;EACA,MAAMuG,qBAAqB,GAAGA,CAAA,KAAM;IAClCjG,gBAAgB,CAAC;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,CAAC;MACVC,sBAAsB,EAAE,CAAC;MACzBC,MAAM,EAAE;IACV,CAAC,CAAC;IACFE,mBAAmB,CAAC,IAAI,CAAC;IACzBV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoG,mBAAmB,GAAIC,SAAS,IAAK;IACzCnG,gBAAgB,CAACmG,SAAS,CAAC;IAC3B3F,mBAAmB,CAAC2F,SAAS,CAAChD,YAAY,CAAC;IAC3CrD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMsG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAI7F,gBAAgB,EAAE;QACpB,MAAMzB,oBAAoB,CAACuH,eAAe,CAAC9F,gBAAgB,EAAER,aAAa,CAAC;QAC3EL,UAAU,CAAC,mCAAmC,CAAC;MACjD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAACwH,eAAe,CAACvG,aAAa,CAAC;QACzDL,UAAU,CAAC,+BAA+B,CAAC;MAC7C;MACAI,kBAAkB,CAAC,KAAK,CAAC;MACzB,MAAMgF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOvF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAMmB,qBAAqB,GAAG,MAAOC,EAAE,IAAK;IAC1C,IAAIC,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAM5H,oBAAoB,CAAC6H,eAAe,CAACH,EAAE,CAAC;QAC9C9G,UAAU,CAAC,kCAAkC,CAAC;QAC9C,MAAMoF,aAAa,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMwB,sBAAsB,GAAGA,CAAA,KAAM;IACnC9F,iBAAiB,CAAC;MAChBC,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IACFE,oBAAoB,CAAC,IAAI,CAAC;IAC1BX,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiG,oBAAoB,GAAIC,UAAU,IAAK;IAC3ChG,iBAAiB,CAACgG,UAAU,CAAC;IAC7BvF,oBAAoB,CAACuF,UAAU,CAAC5D,aAAa,CAAC;IAC9CtC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMmG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAIzF,iBAAiB,EAAE;QACrB,MAAMxC,oBAAoB,CAACkI,gBAAgB,CAAC1F,iBAAiB,EAAET,cAAc,CAAC;QAC9EnB,UAAU,CAAC,oCAAoC,CAAC;MAClD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAACmI,gBAAgB,CAACpG,cAAc,CAAC;QAC3DnB,UAAU,CAAC,gCAAgC,CAAC;MAC9C;MACAkB,mBAAmB,CAAC,KAAK,CAAC;MAC1B,MAAMmE,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOxF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAM8B,sBAAsB,GAAG,MAAOV,EAAE,IAAK;IAC3C,IAAIC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MACtE,IAAI;QACF,MAAM5H,oBAAoB,CAACqI,gBAAgB,CAACX,EAAE,CAAC;QAC/C9G,UAAU,CAAC,mCAAmC,CAAC;QAC/C,MAAMqF,cAAc,CAAC,CAAC;MACxB,CAAC,CAAC,OAAOxF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMgC,qBAAqB,GAAGA,CAAA,KAAM;IAClCrE,gBAAgB,CAAC;MACfC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,sBAAsB,EAAE,IAAI;MAC5BC,iBAAiB,EAAE,EAAE;MACrBC,oBAAoB,EAAE,EAAE;MACxBC,wBAAwB,EAAE,EAAE;MAC5BC,mBAAmB,EAAE,IAAI;MACzBC,cAAc,EAAE,IAAI;MACpBC,uBAAuB,EAAE,IAAI;MAC7BC,uBAAuB,EAAE,IAAI;MAC7BC,uBAAuB,EAAE,IAAI;MAC7BC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,KAAK;MACtBC,iBAAiB,EAAE,KAAK;MACxBC,WAAW,EAAE,KAAK;MAClBC,aAAa,EAAE,KAAK;MACpBC,kBAAkB,EAAE,EAAE;MACtBC,YAAY,EAAE,EAAE;MAChBC,gCAAgC,EAAE,IAAI;MACtCC,WAAW,EAAE,IAAI;MACjBjD,IAAI,EAAE;IACR,CAAC,CAAC;IACFmD,mBAAmB,CAAC,IAAI,CAAC;IACzB1B,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwE,mBAAmB,GAAIC,SAAS,IAAK;IACzCvE,gBAAgB,CAACuE,SAAS,CAAC;IAC3B/C,mBAAmB,CAAC+C,SAAS,CAACC,YAAY,CAAC;IAC3C1E,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM2E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIlD,gBAAgB,EAAE;QACpB,MAAMxF,oBAAoB,CAAC2I,eAAe,CAACnD,gBAAgB,EAAExB,aAAa,CAAC;QAC3EpD,UAAU,CAAC,mCAAmC,CAAC;MACjD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAAC4I,eAAe,CAAC5E,aAAa,CAAC;QACzDpD,UAAU,CAAC,+BAA+B,CAAC;MAC7C;MACAmD,kBAAkB,CAAC,KAAK,CAAC;MACzB,MAAMsC,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAO5F,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAMuC,qBAAqB,GAAG,MAAOnB,EAAE,IAAK;IAC1C,IAAIC,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAM5H,oBAAoB,CAAC8I,eAAe,CAACpB,EAAE,CAAC;QAC9C9G,UAAU,CAAC,kCAAkC,CAAC;QAC9C,MAAMyF,aAAa,CAAC,CAAC;MACvB,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAGA,CAAA,KAAM;IACjChG,eAAe,CAAC;MACdC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClB7B,WAAW,EAAE,EAAE;MACf8B,kBAAkB,EAAE,IAAI;MACxBC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBb,MAAM,EAAE;IACV,CAAC,CAAC;IACFe,kBAAkB,CAAC,IAAI,CAAC;IACxBT,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmG,kBAAkB,GAAIC,GAAG,IAAK;IAClClG,eAAe,CAACkG,GAAG,CAAC;IACpB3F,kBAAkB,CAAC2F,GAAG,CAACC,WAAW,CAAC;IACnCrG,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMsG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,IAAI9F,eAAe,EAAE;QACnB,MAAMrD,oBAAoB,CAACoJ,cAAc,CAAC/F,eAAe,EAAEP,YAAY,CAAC;QACxElC,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAACqJ,cAAc,CAACvG,YAAY,CAAC;QACvDlC,UAAU,CAAC,8BAA8B,CAAC;MAC5C;MACAiC,iBAAiB,CAAC,KAAK,CAAC;MACxB,MAAMqD,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAMgD,oBAAoB,GAAG,MAAO5B,EAAE,IAAK;IACzC,IAAIC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACF,MAAM5H,oBAAoB,CAACuJ,cAAc,CAAC7B,EAAE,CAAC;QAC7C9G,UAAU,CAAC,iCAAiC,CAAC;QAC7C,MAAMsF,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOzF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC6F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;EAED,MAAMkD,kBAAkB,GAAGA,CAAA,kBACzBtJ,OAAA,CAACtD,GAAG;IAAA6M,QAAA,gBACFvJ,OAAA,CAACtD,GAAG;MAAC8M,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnEvJ,OAAA,CAAClD,UAAU;QAAC8M,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DhK,OAAA,CAACjD,MAAM;QACL6M,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEjK,OAAA,CAACnB,OAAO;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAEjD,qBAAsB;QAAAsC,QAAA,EAChC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL3J,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBhK,OAAA,CAAC7C,cAAc;MAACgN,SAAS,EAAExN,KAAM;MAAA4M,QAAA,eAC/BvJ,OAAA,CAAChD,KAAK;QAAAuM,QAAA,gBACJvJ,OAAA,CAAC5C,SAAS;UAAAmM,QAAA,eACRvJ,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhK,OAAA,CAAC/C,SAAS;UAAAsM,QAAA,EACP5I,SAAS,CAACyJ,GAAG,CAAEjD,SAAS;YAAA,IAAAkD,qBAAA;YAAA,oBACvBrK,OAAA,CAAC3C,QAAQ;cAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAEpC,SAAS,CAAClG;cAAc;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjDhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAEpC,SAAS,CAACjG;cAAW;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9ChK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAEpC,SAAS,CAAC/F;cAAO;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1ChK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EACP,EAAAc,qBAAA,GAAAlD,SAAS,CAACmD,eAAe,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BpJ,cAAc,KAAI;cAAG;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACZhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,eACRvJ,OAAA,CAACjC,IAAI;kBACHwM,KAAK,EAAEpD,SAAS,CAAC7F,MAAM,GAAG,IAAI,GAAG,IAAK;kBACtCkJ,KAAK,EAAErD,SAAS,CAAC7F,MAAM,GAAG,SAAS,GAAG,SAAU;kBAChDmJ,IAAI,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,gBACRvJ,OAAA,CAAChC,UAAU;kBAACkM,OAAO,EAAEA,CAAA,KAAMhD,mBAAmB,CAACC,SAAS,CAAE;kBAAAoC,QAAA,eACxDvJ,OAAA,CAACjB,QAAQ;oBAAA8K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbhK,OAAA,CAAChC,UAAU;kBAACkM,OAAO,EAAEA,CAAA,KAAM3C,qBAAqB,CAACJ,SAAS,CAAChD,YAAY,CAAE;kBAAAoF,QAAA,eACvEvJ,OAAA,CAACf,UAAU;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArBC7C,SAAS,CAAChD,YAAY;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsB3B,CAAC;UAAA,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMU,mBAAmB,GAAGA,CAAA,kBAC1B1K,OAAA,CAACtD,GAAG;IAAA6M,QAAA,gBACFvJ,OAAA,CAACtD,GAAG;MAAC8M,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnEvJ,OAAA,CAAClD,UAAU;QAAC8M,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAwB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9DhK,OAAA,CAACjD,MAAM;QACL6M,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEjK,OAAA,CAACnB,OAAO;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAEtC,sBAAuB;QAAA2B,QAAA,EACjC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL3J,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBhK,OAAA,CAAC7C,cAAc;MAACgN,SAAS,EAAExN,KAAM;MAAA4M,QAAA,eAC/BvJ,OAAA,CAAChD,KAAK;QAAAuM,QAAA,gBACJvJ,OAAA,CAAC5C,SAAS;UAAAmM,QAAA,eACRvJ,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhK,OAAA,CAAC/C,SAAS;UAAAsM,QAAA,EACP9H,UAAU,CAAC2I,GAAG,CAAEtC,UAAU,iBACzB9H,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAEzB,UAAU,CAAC/F;YAAe;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnDhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAEzB,UAAU,CAAC9F;YAAK;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAEzB,UAAU,CAAC5F;YAAc;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAEzB,UAAU,CAAC3F;YAAQ;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5ChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,eACRvJ,OAAA,CAACjC,IAAI;gBACHwM,KAAK,EAAEzC,UAAU,CAACzF,MAAM,GAAG,IAAI,GAAG,IAAK;gBACvCmI,KAAK,EAAE1C,UAAU,CAACzF,MAAM,GAAG,SAAS,GAAG,SAAU;gBACjDoI,IAAI,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,gBACRvJ,OAAA,CAAChC,UAAU;gBAACkM,OAAO,EAAEA,CAAA,KAAMrC,oBAAoB,CAACC,UAAU,CAAE;gBAAAyB,QAAA,eAC1DvJ,OAAA,CAACjB,QAAQ;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbhK,OAAA,CAAChC,UAAU;gBAACkM,OAAO,EAAEA,CAAA,KAAMhC,sBAAsB,CAACJ,UAAU,CAAC5D,aAAa,CAAE;gBAAAqF,QAAA,eAC1EvJ,OAAA,CAACf,UAAU;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBClC,UAAU,CAAC5D,aAAa;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoB7B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMW,iBAAiB,GAAGA,CAAA,kBACxB3K,OAAA,CAACtD,GAAG;IAAA6M,QAAA,gBACFvJ,OAAA,CAACtD,GAAG;MAAC8M,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnEvJ,OAAA,CAAClD,UAAU;QAAC8M,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAsB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5DhK,OAAA,CAACjD,MAAM;QACL6M,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEjK,OAAA,CAACnB,OAAO;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAErB,oBAAqB;QAAAU,QAAA,EAC/B;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL3J,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBhK,OAAA,CAAC7C,cAAc;MAACgN,SAAS,EAAExN,KAAM;MAAA4M,QAAA,eAC/BvJ,OAAA,CAAChD,KAAK;QAAAuM,QAAA,gBACJvJ,OAAA,CAAC5C,SAAS;UAAAmM,QAAA,eACRvJ,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhK,OAAA,CAAC/C,SAAS;UAAAsM,QAAA,EACP/G,QAAQ,CAAC4H,GAAG,CAAErB,GAAG,iBAChB/I,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAER,GAAG,CAACjG;YAAa;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1ChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAER,GAAG,CAAChG;YAAc;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3ChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAER,GAAG,CAAC/F;YAAkB;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAER,GAAG,CAAC9F;YAAQ;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,eACRvJ,OAAA,CAACjC,IAAI;gBACHwM,KAAK,EAAExB,GAAG,CAAC1G,MAAM,GAAG,IAAI,GAAG,IAAK;gBAChCmI,KAAK,EAAEzB,GAAG,CAAC1G,MAAM,GAAG,SAAS,GAAG,SAAU;gBAC1CoI,IAAI,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZhK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,gBACRvJ,OAAA,CAAChC,UAAU;gBAACkM,OAAO,EAAEA,CAAA,KAAMpB,kBAAkB,CAACC,GAAG,CAAE;gBAAAQ,QAAA,eACjDvJ,OAAA,CAACjB,QAAQ;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbhK,OAAA,CAAChC,UAAU;gBAACkM,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACL,GAAG,CAACC,WAAW,CAAE;gBAAAO,QAAA,eAC/DvJ,OAAA,CAACf,UAAU;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBCjB,GAAG,CAACC,WAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBpB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMY,kBAAkB,GAAGA,CAAA,kBACzB5K,OAAA,CAACtD,GAAG;IAAA6M,QAAA,gBACFvJ,OAAA,CAACtD,GAAG;MAAC8M,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnEvJ,OAAA,CAAClD,UAAU;QAAC8M,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DhK,OAAA,CAACjD,MAAM;QACL6M,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEjK,OAAA,CAACnB,OAAO;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAE9B,qBAAsB;QAAAmB,QAAA,EAChC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhK,OAAA,CAACrD,KAAK;MAAC6M,EAAE,EAAE;QAAEqB,CAAC,EAAE,CAAC;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACzBvJ,OAAA,CAAC7B,IAAI;QAAC2M,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAxB,QAAA,gBACzBvJ,OAAA,CAAC7B,IAAI;UAAC6M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;YACRyN,SAAS;YACTZ,KAAK,EAAC,SAAS;YACfa,KAAK,EAAE5F,eAAe,CAACI,WAAY;YACnCyF,QAAQ,EAAGC,CAAC,IAAK7F,kBAAkB,CAAC;cAAC,GAAGD,eAAe;cAAEI,WAAW,EAAE0F,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACvFI,UAAU,EAAE;cACVC,cAAc,eAAEzL,OAAA,CAACH,UAAU;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/B;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;UAAC6M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;YAACwN,SAAS;YAAA5B,QAAA,gBACpBvJ,OAAA,CAACpC,UAAU;cAAA2L,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClChK,OAAA,CAACnC,MAAM;cACLuN,KAAK,EAAE5F,eAAe,CAACE,YAAY,IAAI,EAAG;cAC1C2F,QAAQ,EAAGC,CAAC,IAAK7F,kBAAkB,CAAC;gBAAC,GAAGD,eAAe;gBAAEE,YAAY,EAAE4F,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;cAAI,CAAC,CAAE;cAAA7B,QAAA,gBAEhGvJ,OAAA,CAAClC,QAAQ;gBAACsN,KAAK,EAAC,EAAE;gBAAA7B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClCrJ,SAAS,CAACyJ,GAAG,CAAEsB,GAAG,iBACjB1L,OAAA,CAAClC,QAAQ;gBAAwBsN,KAAK,EAAEM,GAAG,CAACvH,YAAa;gBAAAoF,QAAA,EACtDmC,GAAG,CAACzK;cAAc,GADNyK,GAAG,CAACvH,YAAY;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;UAAC6M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;YAACwN,SAAS;YAAA5B,QAAA,gBACpBvJ,OAAA,CAACpC,UAAU;cAAA2L,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnChK,OAAA,CAACnC,MAAM;cACLuN,KAAK,EAAE5F,eAAe,CAACG,aAAa,IAAI,EAAG;cAC3C0F,QAAQ,EAAGC,CAAC,IAAK7F,kBAAkB,CAAC;gBAAC,GAAGD,eAAe;gBAAEG,aAAa,EAAE2F,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;cAAI,CAAC,CAAE;cAAA7B,QAAA,gBAEjGvJ,OAAA,CAAClC,QAAQ;gBAACsN,KAAK,EAAC,EAAE;gBAAA7B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClCvI,UAAU,CAAC2I,GAAG,CAAEuB,IAAI,iBACnB3L,OAAA,CAAClC,QAAQ;gBAA0BsN,KAAK,EAAEO,IAAI,CAACzH,aAAc;gBAAAqF,QAAA,EAC1DoC,IAAI,CAAC5J;cAAe,GADR4J,IAAI,CAACzH,aAAa;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEvB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;UAAC6M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBvJ,OAAA,CAACjD,MAAM;YACL6M,OAAO,EAAC,UAAU;YAClBuB,SAAS;YACTjB,OAAO,EAAE/D,aAAc;YACvBqD,EAAE,EAAE;cAAEoC,MAAM,EAAE;YAAO,CAAE;YAAArC,QAAA,EACxB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEP3J,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBhK,OAAA,CAAC7C,cAAc;MAACgN,SAAS,EAAExN,KAAM;MAAA4M,QAAA,eAC/BvJ,OAAA,CAAChD,KAAK;QAAAuM,QAAA,gBACJvJ,OAAA,CAAC5C,SAAS;UAAAmM,QAAA,eACRvJ,OAAA,CAAC3C,QAAQ;YAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClChK,OAAA,CAAC9C,SAAS;cAAAqM,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZhK,OAAA,CAAC/C,SAAS;UAAAsM,QAAA,EACPlG,SAAS,CAAC+G,GAAG,CAAE9B,SAAS;YAAA,IAAAuD,qBAAA,EAAAC,oBAAA;YAAA,oBACvB9L,OAAA,CAAC3C,QAAQ;cAAAkM,QAAA,gBACPvJ,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAEjB,SAAS,CAACtE;cAAe;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAEjB,SAAS,CAACrE;cAAgB;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnDhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAE,EAAAsC,qBAAA,GAAAvD,SAAS,CAACR,UAAU,cAAA+D,qBAAA,uBAApBA,qBAAA,CAAsB9J,eAAe,KAAI;cAAG;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,EAAE,EAAAuC,oBAAA,GAAAxD,SAAS,CAACnB,SAAS,cAAA2E,oBAAA,uBAAnBA,oBAAA,CAAqB7K,cAAc,KAAI;cAAG;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnEhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,eACRvJ,OAAA,CAACjC,IAAI;kBACHwM,KAAK,EAAEjC,SAAS,CAACjD,WAAW,GAAG,IAAI,GAAG,IAAK;kBAC3CmF,KAAK,EAAElC,SAAS,CAACjD,WAAW,GAAG,SAAS,GAAG,SAAU;kBACrDoF,IAAI,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZhK,OAAA,CAAC9C,SAAS;gBAAAqM,QAAA,gBACRvJ,OAAA,CAAChC,UAAU;kBAACkM,OAAO,EAAEA,CAAA,KAAM7B,mBAAmB,CAACC,SAAS,CAAE;kBAAAiB,QAAA,eACxDvJ,OAAA,CAACjB,QAAQ;oBAAA8K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbhK,OAAA,CAAChC,UAAU;kBAACkM,OAAO,EAAEA,CAAA,KAAMvB,qBAAqB,CAACL,SAAS,CAACC,YAAY,CAAE;kBAAAgB,QAAA,eACvEvJ,OAAA,CAACf,UAAU;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnBC1B,SAAS,CAACC,YAAY;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoB3B,CAAC;UAAA,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,oBACEhK,OAAA,CAACtD,GAAG;IAAC8M,EAAE,EAAE;MAAEuC,KAAK,EAAE;IAAO,CAAE;IAAAxC,QAAA,GACxBhJ,KAAK,iBACJP,OAAA,CAAC/B,KAAK;MAAC+N,QAAQ,EAAC,OAAO;MAACxC,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAACsC,OAAO,EAAEA,CAAA,KAAMzL,QAAQ,CAAC,EAAE,CAAE;MAAA+I,QAAA,EAChEhJ;IAAK;MAAAsJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAvJ,OAAO,iBACNT,OAAA,CAAC/B,KAAK;MAAC+N,QAAQ,EAAC,SAAS;MAACxC,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAACsC,OAAO,EAAEA,CAAA,KAAMvL,UAAU,CAAC,EAAE,CAAE;MAAA6I,QAAA,EACpE9I;IAAO;MAAAoJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAEDhK,OAAA,CAACrD,KAAK;MAAC6M,EAAE,EAAE;QAAEuC,KAAK,EAAE,MAAM;QAAEpC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClCvJ,OAAA,CAACpD,IAAI;QACHwO,KAAK,EAAEjL,QAAS;QAChBkL,QAAQ,EAAEvE,eAAgB;QAC1BoF,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBvC,OAAO,EAAC,YAAY;QACpBwC,aAAa,EAAC,MAAM;QAAA7C,QAAA,gBAEpBvJ,OAAA,CAACnD,GAAG;UAACwP,IAAI,eAAErM,OAAA,CAACX,YAAY;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDhK,OAAA,CAACnD,GAAG;UAACwP,IAAI,eAAErM,OAAA,CAACT,YAAY;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAY;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDhK,OAAA,CAACnD,GAAG;UAACwP,IAAI,eAAErM,OAAA,CAACP,cAAc;YAAAoK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAU;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDhK,OAAA,CAACnD,GAAG;UAACwP,IAAI,eAAErM,OAAA,CAACL,SAAS;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAERhK,OAAA,CAACtD,GAAG;MAAC8M,EAAE,EAAE;QAAE8C,EAAE,EAAE;MAAE,CAAE;MAAA/C,QAAA,GAChBpJ,QAAQ,KAAK,CAAC,IAAImJ,kBAAkB,CAAC,CAAC,EACtCnJ,QAAQ,KAAK,CAAC,IAAIuK,mBAAmB,CAAC,CAAC,EACvCvK,QAAQ,KAAK,CAAC,IAAIwK,iBAAiB,CAAC,CAAC,EACrCxK,QAAQ,KAAK,CAAC,IAAIyK,kBAAkB,CAAC,CAAC;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGNhK,OAAA,CAAC1C,MAAM;MAACiP,IAAI,EAAE1L,eAAgB;MAACoL,OAAO,EAAEA,CAAA,KAAMnL,kBAAkB,CAAC,KAAK,CAAE;MAAC0L,QAAQ,EAAC,IAAI;MAACrB,SAAS;MAAA5B,QAAA,gBAC9FvJ,OAAA,CAACzC,WAAW;QAAAgM,QAAA,EACThI,gBAAgB,GAAG,oBAAoB,GAAG;MAAiB;QAAAsI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACdhK,OAAA,CAACxC,aAAa;QAAA+L,QAAA,eACZvJ,OAAA,CAAC7B,IAAI;UAAC2M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACxCvJ,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtBa,KAAK,EAAErK,aAAa,CAACE,cAAe;cACpCoK,QAAQ,EAAGC,CAAC,IAAKtK,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEE,cAAc,EAAEqK,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACtFqB,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;cAACwN,SAAS;cAAA5B,QAAA,gBACpBvJ,OAAA,CAACpC,UAAU;gBAAA2L,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxChK,OAAA,CAACnC,MAAM;gBACLuN,KAAK,EAAErK,aAAa,CAACI,kBAAkB,IAAI,EAAG;gBAC9CkK,QAAQ,EAAGC,CAAC,IAAKtK,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEI,kBAAkB,EAAEmK,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;gBAAI,CAAC,CAAE;gBAAA7B,QAAA,gBAElGvJ,OAAA,CAAClC,QAAQ;kBAACsN,KAAK,EAAC,EAAE;kBAAA7B,QAAA,EAAC;gBAA8B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC3DrJ,SAAS,CAAC+L,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvL,OAAO,GAAG,CAAC,CAAC,CAACgJ,GAAG,CAAEsB,GAAG,iBAC5C1L,OAAA,CAAClC,QAAQ;kBAAwBsN,KAAK,EAAEM,GAAG,CAACvH,YAAa;kBAAAoF,QAAA,EACtDmC,GAAG,CAACzK;gBAAc,GADNyK,GAAG,CAACvH,YAAY;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,aAAa;cACnBa,KAAK,EAAErK,aAAa,CAACG,WAAY;cACjCmK,QAAQ,EAAGC,CAAC,IAAKtK,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEG,WAAW,EAAEoK,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACnFwB,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,wBAAwB;cAC9BuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAErK,aAAa,CAACM,sBAAuB;cAC5CgK,QAAQ,EAAGC,CAAC,IAAKtK,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEM,sBAAsB,EAAE0L,QAAQ,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAC,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAElM,aAAa,CAACO,MAAO;gBAC9B+J,QAAQ,EAAGC,CAAC,IAAKtK,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEO,MAAM,EAAEgK,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CACF;cACDO,KAAK,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhK,OAAA,CAACvC,aAAa;QAAA8L,QAAA,gBACZvJ,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEA,CAAA,KAAMpJ,kBAAkB,CAAC,KAAK,CAAE;UAAAyI,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEhK,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAE9C,mBAAoB;UAACwC,OAAO,EAAC,WAAW;UAAAL,QAAA,EACtDhI,gBAAgB,GAAG,UAAU,GAAG;QAAM;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThK,OAAA,CAAC1C,MAAM;MAACiP,IAAI,EAAE5K,gBAAiB;MAACsK,OAAO,EAAEA,CAAA,KAAMrK,mBAAmB,CAAC,KAAK,CAAE;MAAC4K,QAAQ,EAAC,IAAI;MAACrB,SAAS;MAAA5B,QAAA,gBAChGvJ,OAAA,CAACzC,WAAW;QAAAgM,QAAA,EACTjH,iBAAiB,GAAG,qBAAqB,GAAG;MAAkB;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACdhK,OAAA,CAACxC,aAAa;QAAA+L,QAAA,eACZvJ,OAAA,CAAC7B,IAAI;UAAC2M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACxCvJ,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,iBAAiB;cACvBa,KAAK,EAAEvJ,cAAc,CAACE,eAAgB;cACtCsJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEE,eAAe,EAAEuJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACzFqB,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,OAAO;cACba,KAAK,EAAEvJ,cAAc,CAACG,KAAM;cAC5BqJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEG,KAAK,EAAEsJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtBuC,IAAI,EAAC,OAAO;cACZ1B,KAAK,EAAEvJ,cAAc,CAACK,cAAe;cACrCmJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEK,cAAc,EAAEoJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAEvJ,cAAc,CAACM,QAAS;cAC/BkJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEM,QAAQ,EAAEmJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAEvJ,cAAc,CAACI,QAAS;cAC/BoJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEI,QAAQ,EAAEqJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,MAAM;cACZa,KAAK,EAAEvJ,cAAc,CAACO,IAAK;cAC3BiJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEO,IAAI,EAAEkJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC9EwB,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEpL,cAAc,CAACQ,MAAO;gBAC/BgJ,QAAQ,EAAGC,CAAC,IAAKxJ,iBAAiB,CAAC;kBAAC,GAAGD,cAAc;kBAAEQ,MAAM,EAAEiJ,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CACF;cACDO,KAAK,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhK,OAAA,CAACvC,aAAa;QAAA8L,QAAA,gBACZvJ,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEA,CAAA,KAAMtI,mBAAmB,CAAC,KAAK,CAAE;UAAA2H,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnEhK,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEnC,oBAAqB;UAAC6B,OAAO,EAAC,WAAW;UAAAL,QAAA,EACvDjH,iBAAiB,GAAG,UAAU,GAAG;QAAM;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThK,OAAA,CAAC1C,MAAM;MAACiP,IAAI,EAAE7J,cAAe;MAACuJ,OAAO,EAAEA,CAAA,KAAMtJ,iBAAiB,CAAC,KAAK,CAAE;MAAC6J,QAAQ,EAAC,IAAI;MAACrB,SAAS;MAAA5B,QAAA,gBAC5FvJ,OAAA,CAACzC,WAAW;QAAAgM,QAAA,EACTpG,eAAe,GAAG,mBAAmB,GAAG;MAAgB;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACdhK,OAAA,CAACxC,aAAa;QAAA+L,QAAA,eACZvJ,OAAA,CAAC7B,IAAI;UAAC2M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACxCvJ,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,eAAe;cACrBa,KAAK,EAAExI,YAAY,CAACE,aAAc;cAClCuI,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEE,aAAa,EAAEwI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACnFqB,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtBa,KAAK,EAAExI,YAAY,CAACG,cAAe;cACnCsI,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEG,cAAc,EAAEuI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,aAAa;cACnBa,KAAK,EAAExI,YAAY,CAAC1B,WAAY;cAChCmK,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAE1B,WAAW,EAAEoK,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjFwB,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,oBAAoB;cAC1BuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAExI,YAAY,CAACI,kBAAkB,IAAI,EAAG;cAC7CqI,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEI,kBAAkB,EAAE+J,QAAQ,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAExI,YAAY,CAACK,QAAS;cAC7BoI,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEK,QAAQ,EAAEqI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAErK,YAAY,CAACP,MAAO;gBAC7BgJ,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEP,MAAM,EAAEiJ,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CACF;cACDO,KAAK,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,eAAe;cACrBa,KAAK,EAAExI,YAAY,CAACM,aAAc;cAClCmI,QAAQ,EAAGC,CAAC,IAAKzI,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEM,aAAa,EAAEoI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhK,OAAA,CAACvC,aAAa;QAAA8L,QAAA,gBACZvJ,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEA,CAAA,KAAMvH,iBAAiB,CAAC,KAAK,CAAE;UAAA4G,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjEhK,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEjB,kBAAmB;UAACW,OAAO,EAAC,WAAW;UAAAL,QAAA,EACrDpG,eAAe,GAAG,UAAU,GAAG;QAAM;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThK,OAAA,CAAC1C,MAAM;MAACiP,IAAI,EAAE3I,eAAgB;MAACqI,OAAO,EAAEA,CAAA,KAAMpI,kBAAkB,CAAC,KAAK,CAAE;MAAC2I,QAAQ,EAAC,IAAI;MAACrB,SAAS;MAAA5B,QAAA,gBAC9FvJ,OAAA,CAACzC,WAAW;QAAAgM,QAAA,EACTjE,gBAAgB,GAAG,oBAAoB,GAAG;MAAiB;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACdhK,OAAA,CAACxC,aAAa;QAAA+L,QAAA,eACZvJ,OAAA,CAAC7B,IAAI;UAAC2M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACxCvJ,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,iBAAiB;cACvBa,KAAK,EAAEtH,aAAa,CAACE,eAAgB;cACrCqH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEE,eAAe,EAAEsH,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACvFqB,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,kBAAkB;cACxBa,KAAK,EAAEtH,aAAa,CAACG,gBAAiB;cACtCoH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEG,gBAAgB,EAAEqH,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;cAACwN,SAAS;cAACsB,QAAQ;cAAAlD,QAAA,gBAC7BvJ,OAAA,CAACpC,UAAU;gBAAA2L,QAAA,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClChK,OAAA,CAACnC,MAAM;gBACLuN,KAAK,EAAEtH,aAAa,CAACK,YAAY,IAAI,EAAG;gBACxCkH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEK,YAAY,EAAEmH,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;gBAAI,CAAC,CAAE;gBAAA7B,QAAA,EAE3F5I,SAAS,CAACyJ,GAAG,CAAEsB,GAAG,iBACjB1L,OAAA,CAAClC,QAAQ;kBAAwBsN,KAAK,EAAEM,GAAG,CAACvH,YAAa;kBAAAoF,QAAA,EACtDmC,GAAG,CAACzK;gBAAc,GADNyK,GAAG,CAACvH,YAAY;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;cAACwN,SAAS;cAAA5B,QAAA,gBACpBvJ,OAAA,CAACpC,UAAU;gBAAA2L,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnChK,OAAA,CAACnC,MAAM;gBACLuN,KAAK,EAAEtH,aAAa,CAACI,aAAa,IAAI,EAAG;gBACzCmH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEI,aAAa,EAAEoH,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;gBAAI,CAAC,CAAE;gBAAA7B,QAAA,gBAE7FvJ,OAAA,CAAClC,QAAQ;kBAACsN,KAAK,EAAC,EAAE;kBAAA7B,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EACpCvI,UAAU,CAAC2I,GAAG,CAAEuB,IAAI,iBACnB3L,OAAA,CAAClC,QAAQ;kBAA0BsN,KAAK,EAAEO,IAAI,CAACzH,aAAc;kBAAAqF,QAAA,EAC1DoC,IAAI,CAAC5J;gBAAe,GADR4J,IAAI,CAACzH,aAAa;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACrC,WAAW;cAACwN,SAAS;cAAA5B,QAAA,gBACpBvJ,OAAA,CAACpC,UAAU;gBAAA2L,QAAA,EAAC;cAAmB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5ChK,OAAA,CAACnC,MAAM;gBACLuN,KAAK,EAAEtH,aAAa,CAACM,sBAAsB,IAAI,EAAG;gBAClDiH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEM,sBAAsB,EAAEkH,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;gBAAI,CAAC,CAAE;gBAAA7B,QAAA,gBAEtGvJ,OAAA,CAAClC,QAAQ;kBAACsN,KAAK,EAAC,EAAE;kBAAA7B,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EACpCxH,QAAQ,CAAC4H,GAAG,CAAErB,GAAG,iBAChB/I,OAAA,CAAClC,QAAQ;kBAAuBsN,KAAK,EAAErC,GAAG,CAACC,WAAY;kBAAAO,QAAA,EACpDR,GAAG,CAACjG;gBAAa,GADLiG,GAAG,CAACC,WAAW;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEpB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,mBAAmB;cACzBa,KAAK,EAAEtH,aAAa,CAACO,iBAAkB;cACvCgH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEO,iBAAiB,EAAEiH,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACzFwB,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,0BAA0B;cAChCa,KAAK,EAAEtH,aAAa,CAACS,wBAAyB;cAC9C8G,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAES,wBAAwB,EAAE+G,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,uBAAuB;cAC7BuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtH,aAAa,CAACU,mBAAmB,IAAI,EAAG;cAC/C6G,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEU,mBAAmB,EAAE0I,UAAU,CAAC5B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,cAAc;cACpBuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtH,aAAa,CAACW,cAAc,IAAI,EAAG;cAC1C4G,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEW,cAAc,EAAEyI,UAAU,CAAC5B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,mBAAgB;cACtBuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtH,aAAa,CAACY,uBAAuB,IAAI,EAAG;cACnD2G,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEY,uBAAuB,EAAEqI,QAAQ,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,mBAAgB;cACtBuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtH,aAAa,CAACa,uBAAuB,IAAI,EAAG;cACnD0G,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEa,uBAAuB,EAAEoI,QAAQ,CAACzB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,8BAAyB;cAC/BuC,IAAI,EAAC,QAAQ;cACbK,IAAI,EAAC,QAAQ;cACb/B,KAAK,EAAEtH,aAAa,CAACsB,gCAAgC,IAAI,EAAG;cAC5DiG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEsB,gCAAgC,EAAE8H,UAAU,CAAC5B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,2BAA2B;cACjCuC,IAAI,EAAC,QAAQ;cACb1B,KAAK,EAAEtH,aAAa,CAACc,uBAAuB,IAAI,EAAG;cACnDyG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEc,uBAAuB,EAAEsI,UAAU,CAAC5B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACe,aAAc;gBACrCwG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEe,aAAa,EAAEyG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACF;cACDO,KAAK,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACgB,eAAgB;gBACvCuG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEgB,eAAe,EAAEwG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CACF;cACDO,KAAK,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACiB,iBAAkB;gBACzCsG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEiB,iBAAiB,EAAEuG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CACF;cACDO,KAAK,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACuB,WAAY;gBACnCgG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEuB,WAAW,EAAEiG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CACF;cACDO,KAAK,EAAC;YAAa;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACkB,WAAY;gBACnCqG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEkB,WAAW,EAAEsG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CACF;cACDO,KAAK,EAAC;YAAa;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvBvJ,OAAA,CAACtB,gBAAgB;cACfsO,OAAO,eACLhN,OAAA,CAACrB,MAAM;gBACLsO,OAAO,EAAEnJ,aAAa,CAACmB,aAAc;gBACrCoG,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEmB,aAAa,EAAEqG,CAAC,CAACC,MAAM,CAAC0B;gBAAO,CAAC;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACF;cACDO,KAAK,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPhK,OAAA,CAAC7B,IAAI;YAAC6M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChBvJ,OAAA,CAACtC,SAAS;cACRyN,SAAS;cACTZ,KAAK,EAAC,MAAM;cACZa,KAAK,EAAEtH,aAAa,CAAC1B,IAAK;cAC1BiJ,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAE1B,IAAI,EAAEkJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5EwB,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhK,OAAA,CAACvC,aAAa;QAAA8L,QAAA,gBACZvJ,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,KAAK,CAAE;UAAA0F,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEhK,OAAA,CAACjD,MAAM;UAACmN,OAAO,EAAE1B,mBAAoB;UAACoB,OAAO,EAAC,WAAW;UAAAL,QAAA,EACtDjE,gBAAgB,GAAG,UAAU,GAAG;QAAM;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9J,EAAA,CA1oCID,oBAAoB;AAAAmN,EAAA,GAApBnN,oBAAoB;AA4oC1B,eAAeA,oBAAoB;AAAC,IAAAmN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}