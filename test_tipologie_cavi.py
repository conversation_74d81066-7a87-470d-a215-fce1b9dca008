#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per inserire alcune tipologie di cavi di esempio nel database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.database_pg import Database
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def inserisci_tipologie_esempio():
    """Inserisce alcune tipologie di cavi di esempio nel database."""
    
    db = Database()
    
    try:
        conn = db.get_connection()
        if not conn:
            logger.error("Impossibile connettersi al database")
            return False
            
        with conn.cursor() as cursor:
            # Verifica che ci siano categorie e produttori
            cursor.execute("SELECT COUNT(*) FROM categorie_cavi")
            count_cat = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM produttori_cavi")
            count_prod = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM standard_cavi")
            count_std = cursor.fetchone()[0]
            
            logger.info(f"Database stato: {count_cat} categorie, {count_prod} produttori, {count_std} standard")
            
            if count_cat == 0 or count_prod == 0 or count_std == 0:
                logger.error("Database non inizializzato correttamente")
                return False
            
            # Recupera alcuni ID per gli esempi
            cursor.execute("SELECT id_categoria FROM categorie_cavi WHERE nome_categoria LIKE '%Bassa Tensione%' LIMIT 1")
            result = cursor.fetchone()
            if not result:
                logger.error("Categoria Bassa Tensione non trovata")
                return False
            categoria_bt_id = result[0]
            
            cursor.execute("SELECT id_produttore FROM produttori_cavi WHERE nome_produttore LIKE '%Prysmian%' LIMIT 1")
            result = cursor.fetchone()
            if not result:
                logger.error("Produttore Prysmian non trovato")
                return False
            prysmian_id = result[0]
            
            cursor.execute("SELECT id_standard FROM standard_cavi WHERE nome_standard LIKE '%CEI 20-13%' LIMIT 1")
            result = cursor.fetchone()
            standard_id = result[0] if result else None
            
            # Verifica se ci sono già tipologie
            cursor.execute("SELECT COUNT(*) FROM tipologie_cavi")
            count_tip = cursor.fetchone()[0]
            
            if count_tip > 0:
                logger.info(f"Ci sono già {count_tip} tipologie nel database")
                return True
            
            # Inserisci alcune tipologie di esempio
            tipologie_esempio = [
                {
                    'codice_prodotto': 'N07V-K 1x1.5',
                    'nome_commerciale': 'Cavo unipolare per impianti BT',
                    'id_produttore': prysmian_id,
                    'id_categoria': categoria_bt_id,
                    'id_standard_principale': standard_id,
                    'descrizione_breve': 'Cavo unipolare con isolamento in PVC per tensioni fino a 450/750V',
                    'materiale_guaina_esterna': 'PVC',
                    'diametro_esterno_mm': 3.2,
                    'peso_kg_per_km': 18.5,
                    'temperatura_min_celsius': -15,
                    'temperatura_max_celsius': 70,
                    'resistente_fiamma': True,
                    'per_esterno': False,
                    'per_interrato': False,
                    'prezzo_indicativo_euro_per_metro': 0.45,
                    'disponibile': True
                },
                {
                    'codice_prodotto': 'N07V-K 1x2.5',
                    'nome_commerciale': 'Cavo unipolare per impianti BT',
                    'id_produttore': prysmian_id,
                    'id_categoria': categoria_bt_id,
                    'id_standard_principale': standard_id,
                    'descrizione_breve': 'Cavo unipolare con isolamento in PVC per tensioni fino a 450/750V',
                    'materiale_guaina_esterna': 'PVC',
                    'diametro_esterno_mm': 3.8,
                    'peso_kg_per_km': 28.2,
                    'temperatura_min_celsius': -15,
                    'temperatura_max_celsius': 70,
                    'resistente_fiamma': True,
                    'per_esterno': False,
                    'per_interrato': False,
                    'prezzo_indicativo_euro_per_metro': 0.68,
                    'disponibile': True
                },
                {
                    'codice_prodotto': 'FG16OR16 0.6/1kV 3x95+50',
                    'nome_commerciale': 'Cavo per energia MT resistente al fuoco',
                    'id_produttore': prysmian_id,
                    'id_categoria': categoria_bt_id,
                    'id_standard_principale': standard_id,
                    'descrizione_breve': 'Cavo per energia media tensione con caratteristiche di resistenza al fuoco',
                    'materiale_guaina_esterna': 'LSZH',
                    'diametro_esterno_mm': 28.5,
                    'peso_kg_per_km': 2850,
                    'temperatura_min_celsius': -20,
                    'temperatura_max_celsius': 90,
                    'resistente_fiamma': True,
                    'resistente_uv': True,
                    'per_esterno': True,
                    'per_interrato': True,
                    'prezzo_indicativo_euro_per_metro': 15.75,
                    'disponibile': True
                }
            ]
            
            for tipologia in tipologie_esempio:
                # Costruisci la query di inserimento
                columns = ', '.join(tipologia.keys())
                placeholders = ', '.join(['%s'] * len(tipologia))
                values = list(tipologia.values())
                
                query = f"""
                INSERT INTO tipologie_cavi ({columns})
                VALUES ({placeholders})
                """
                
                cursor.execute(query, values)
                logger.info(f"Inserita tipologia: {tipologia['codice_prodotto']}")
            
            # Inserisci alcune specifiche tecniche per la prima tipologia
            cursor.execute("SELECT id_tipologia FROM tipologie_cavi WHERE codice_prodotto = 'N07V-K 1x1.5' LIMIT 1")
            result = cursor.fetchone()
            if result:
                tipologia_id = result[0]
                
                specifiche = [
                    ('Sezione Conduttore', '1.5', 'mm²', 'number', 'Elettrico', 1, True),
                    ('Numero Conduttori', '1', '', 'number', 'Elettrico', 2, True),
                    ('Tensione Nominale', '450/750', 'V', 'text', 'Elettrico', 3, True),
                    ('Materiale Conduttore', 'Rame', '', 'text', 'Materiali', 1, True),
                    ('Classe Conduttore', '5', '', 'text', 'Elettrico', 4, False),
                    ('Resistenza DC 20°C', '12.1', 'Ω/km', 'number', 'Elettrico', 5, False)
                ]
                
                for nome, valore, unita, tipo, gruppo, ordine, obbligatorio in specifiche:
                    cursor.execute("""
                        INSERT INTO specifiche_tecniche_cavi 
                        (id_tipologia, nome_attributo, valore_attributo, unita_misura, tipo_dato, gruppo_attributo, ordine_visualizzazione, obbligatorio)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (tipologia_id, nome, valore, unita, tipo, gruppo, ordine, obbligatorio))
                
                logger.info(f"Inserite {len(specifiche)} specifiche tecniche per {tipologia['codice_prodotto']}")
            
            conn.commit()
            logger.info("✅ Tipologie di esempio inserite con successo!")
            
            # Verifica finale
            cursor.execute("SELECT COUNT(*) FROM tipologie_cavi")
            count_final = cursor.fetchone()[0]
            logger.info(f"📊 Totale tipologie nel database: {count_final}")
            
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante l'inserimento: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🔄 Inserimento tipologie di cavi di esempio...")
    
    # Prima inizializza il database
    db = Database()
    db.inizializza_database()
    
    # Poi inserisci gli esempi
    success = inserisci_tipologie_esempio()
    
    if success:
        print("✅ Operazione completata con successo!")
    else:
        print("❌ Operazione fallita!")
        sys.exit(1)
